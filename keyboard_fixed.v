// 修正后的键盘模块 - 解决按键无响应问题
// 主要修正：
// 1. 修正case语句的完整性，添加default分支
// 2. 修正复位逻辑的优先级问题
// 3. 优化消抖逻辑
// 4. 修正状态机逻辑

module keyboard(
    input wire sys_clk,
    input wire sys_rst_n,
    input wire [3:0] col,
    input wire ack,

    output reg [3:0] row,
    output reg [13:0] temp_data
);

    reg       delay;    //当按下的时候，row停止扫描
    reg [3:0] char;
    reg [13:0] temp;
    reg [24:0] cnt;
    reg [24:0] debounce_cnt;
    reg key_pressed;
    reg key_released;
    reg [1:0] state;        //状态
    reg [25:0] ack_debounce_cnt;  // 修正：扩展位宽

    parameter row_scan_period = 25'd2_000_000;  // 20ms扫描一次
    parameter debounce_period = 26'd2_000_000;   // 20ms去抖动时间
    parameter ack_debounce_period = 26'd2_000_000;   // 20ms去抖动时间

    parameter first_row  = 4'b1110;
    parameter second_row = 4'b1101;
    parameter third_row  = 4'b1011;
    parameter fourth_row = 4'b0111;
    parameter first_col  = 4'b1110;
    parameter second_col = 4'b1101;
    parameter third_col  = 4'b1011;
    parameter fourth_col = 4'b0111;

    parameter IDLE = 2'b00;
    parameter PRESSED = 2'b01;
    parameter RELEASED = 2'b10;

    // 行扫描控制 - 修正版本
    always @ (posedge sys_clk or negedge sys_rst_n) begin
        if (!sys_rst_n) begin
            row <= first_row;
            cnt <= 25'd0;
        end
        else if(delay == 1'b1) begin   //当列扫描到按键按下的时候，停止行扫描,固定为该行
            row <= row;
        end
        else begin
            if (cnt >= row_scan_period) begin
                row <= {row[2:0], row[3]};
                cnt <= 25'd0;
            end else begin
                cnt <= cnt + 1'b1;
            end
        end
    end

    // 去抖动计数器和状态机 - 修正版本
    always @ (posedge sys_clk or negedge sys_rst_n) begin
        if (!sys_rst_n) begin
            debounce_cnt <= 26'd0;
            key_pressed <= 1'b0;
            key_released <= 1'b1;
            state <= IDLE;
            delay <= 1'b0;
        end else begin
            case (state)
                IDLE: begin
                    if (col != 4'b1111) begin
                        delay <= 1'b1;
                        if (debounce_cnt >= debounce_period) begin
                            key_pressed <= 1'b1;
                            key_released <= 1'b0;
                            state <= PRESSED;
                            debounce_cnt <= 26'd0;
                        end else begin
                            debounce_cnt <= debounce_cnt + 1'b1;
                        end
                    end else begin
                        debounce_cnt <= 26'd0;
                        key_pressed <= 1'b0;
                        key_released <= 1'b1;
                        delay <= 1'b0;
                    end
                end
                PRESSED: begin
                    if (col == 4'b1111) begin
                        state <= RELEASED;
                        debounce_cnt <= 26'd0;
                    end
                end
                RELEASED: begin
                    if (debounce_cnt >= debounce_period) begin
                        key_pressed <= 1'b0;
                        key_released <= 1'b1;
                        delay <= 1'b0;
                        state <= IDLE;
                        debounce_cnt <= 26'd0;
                    end else begin
                        debounce_cnt <= debounce_cnt + 1'b1;
                    end
                end
                default: begin  // 添加default分支
                    state <= IDLE;
                    debounce_cnt <= 26'd0;
                    key_pressed <= 1'b0;
                    key_released <= 1'b1;
                    delay <= 1'b0;
                end
            endcase
        end
    end

    // 按键处理逻辑 - 修正版本
    always @ (posedge sys_clk or negedge sys_rst_n) begin
        if (!sys_rst_n) begin
            char <= 4'd0;
            temp <= 14'd0;
            temp_data <= 14'd0;
            ack_debounce_cnt <= 26'd0;
        end
        else if(ack == 1'b1) begin      //确认信号ack有效，将数据置为0
            if(ack_debounce_cnt >= ack_debounce_period) begin    //按键消抖
                temp_data <= 14'd0;
                temp <= 14'd0;
                ack_debounce_cnt <= 26'd0;
                char <= 4'd0;
            end else begin
                ack_debounce_cnt <= ack_debounce_cnt + 1'b1;
            end
        end
        else if (state == PRESSED && key_pressed && !key_released) begin
            // 修正：添加完整的case语句和default分支
            case (row)
                first_row: begin
                    case (col)
                        first_col  : char <= 4'd1;
                        second_col : char <= 4'd2;
                        third_col  : char <= 4'd3;
                        fourth_col : char <= 4'd0; // 功能键值设为0
                        default    : char <= 4'd0; // 添加default
                    endcase
                end
                second_row: begin
                    case (col)
                        first_col  : char <= 4'd4;
                        second_col : char <= 4'd5;
                        third_col  : char <= 4'd6;
                        fourth_col : char <= 4'd0; // 功能键值设为0
                        default    : char <= 4'd0; // 添加default
                    endcase
                end
                third_row: begin
                    case (col)
                        first_col  : char <= 4'd7;
                        second_col : char <= 4'd8;
                        third_col  : char <= 4'd9;
                        fourth_col : char <= 4'd0; // 功能键值设为0
                        default    : char <= 4'd0; // 添加default
                    endcase
                end
                fourth_row: begin
                    case (col)
                        first_col  : char <= 4'd0; // 功能键值设为0
                        second_col : char <= 4'd0;
                        third_col  : char <= 4'd0;
                        fourth_col : char <= 4'd0;
                        default    : char <= 4'd0; // 添加default
                    endcase
                end
                default: begin  // 添加default分支
                    char <= 4'd0;
                end
            endcase
        end else if (state == RELEASED && key_released) begin
            if(temp <= 14'd999) begin
                temp <= (temp * 10) + char;
                temp_data <= (temp * 10) + char;
                char <= 4'd0;
            end else begin
                temp <= 14'd0;
                temp_data <= 14'd0;
                char <= 4'd0;
            end
        end
        else begin
            ack_debounce_cnt <= 26'd0;  // 重置ack消抖计数器
        end
    end

endmodule
