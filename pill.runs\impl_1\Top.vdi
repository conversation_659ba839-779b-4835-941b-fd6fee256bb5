#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sat May 31 15:18:32 2025
# Process ID: 14816
# Current directory: E:/Final_pro/pill/pill.runs/impl_1
# Command line: vivado.exe -log Top.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source Top.tcl -notrace
# Log file: E:/Final_pro/pill/pill.runs/impl_1/Top.vdi
# Journal file: E:/Final_pro/pill/pill.runs/impl_1\vivado.jou
#-----------------------------------------------------------
source Top.tcl -notrace
Command: link_design -top Top -part xc7a100tfgg484-1
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Netlist 29-17] Analyzing 649 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:42]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:43]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:44]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:45]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:50]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:51]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:52]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:53]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:62]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:63]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:64]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:65]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:66]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:67]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:68]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:69]
Finished Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 669.402 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

7 Infos, 0 Warnings, 16 Critical Warnings and 0 Errors encountered.
link_design completed successfully
link_design: Time (s): cpu = 00:00:04 ; elapsed = 00:00:05 . Memory (MB): peak = 669.402 ; gain = 344.512
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.360 . Memory (MB): peak = 678.914 ; gain = 9.512

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: 16285b2af

Time (s): cpu = 00:00:05 ; elapsed = 00:00:06 . Memory (MB): peak = 1199.641 ; gain = 520.727

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: 16285b2af

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.125 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Opt 31-389] Phase Retarget created 0 cells and removed 0 cells

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 2 Constant propagation | Checksum: 14ee0fb6c

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.168 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Opt 31-389] Phase Constant propagation created 0 cells and removed 0 cells

Phase 3 Sweep
Phase 3 Sweep | Checksum: dd3814f0

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.239 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 0 cells

Phase 4 BUFG optimization
Phase 4 BUFG optimization | Checksum: dd3814f0

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.337 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: 1aafd46ab

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.544 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: 1aafd46ab

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.558 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |               0  |               0  |                                              0  |
|  Constant propagation         |               0  |               0  |                                              0  |
|  Sweep                        |               0  |               0  |                                              0  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                              0  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1296.863 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 1aafd46ab

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.569 . Memory (MB): peak = 1296.863 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
Ending Power Optimization Task | Checksum: 1aafd46ab

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 1296.863 ; gain = 0.000

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: 1aafd46ab

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1296.863 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1296.863 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 1aafd46ab

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
23 Infos, 0 Warnings, 16 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:07 ; elapsed = 00:00:07 . Memory (MB): peak = 1296.863 ; gain = 627.461
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.020 . Memory (MB): peak = 1296.863 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1296.863 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file Top_drc_opted.rpt -pb Top_drc_opted.pb -rpx Top_drc_opted.rpx
Command: report_drc -file Top_drc_opted.rpt -pb Top_drc_opted.pb -rpx Top_drc_opted.rpx
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'D:/Xilinx/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/Final_pro/pill/pill.runs/impl_1/Top_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1296.863 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: 1120022b8

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1296.863 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1296.863 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 1a3812cb7

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.663 . Memory (MB): peak = 1317.344 ; gain = 20.480

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 202436904

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 202436904

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1343.457 ; gain = 46.594
Phase 1 Placer Initialization | Checksum: 202436904

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 1af7ad806

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 2.2 Physical Synthesis In Placer
INFO: [Physopt 32-65] No nets found for high-fanout optimization.
INFO: [Physopt 32-232] Optimized 0 net. Created 0 new instance.
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-456] No candidate cells for DSP register optimization found in the design.
INFO: [Physopt 32-775] End 2 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-677] No candidate cells for Shift Register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-526] No candidate cells for BRAM register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-949] No candidate nets found for HD net replication
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1343.457 ; gain = 0.000

Summary of Physical Synthesis Optimizations
============================================


----------------------------------------------------------------------------------------------------------------------------------------
|  Optimization                  |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
----------------------------------------------------------------------------------------------------------------------------------------
|  Very High Fanout              |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  DSP Register                  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Shift Register                |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  BRAM Register                 |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  HD Interface Net Replication  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Total                         |            0  |              0  |                     0  |           0  |           5  |  00:00:00  |
----------------------------------------------------------------------------------------------------------------------------------------


Phase 2.2 Physical Synthesis In Placer | Checksum: 17be1af15

Time (s): cpu = 00:00:07 ; elapsed = 00:00:05 . Memory (MB): peak = 1343.457 ; gain = 46.594
Phase 2 Global Placement | Checksum: 1f4dcbf60

Time (s): cpu = 00:00:07 ; elapsed = 00:00:05 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 1f4dcbf60

Time (s): cpu = 00:00:07 ; elapsed = 00:00:05 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 195fa1f24

Time (s): cpu = 00:00:08 ; elapsed = 00:00:06 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 19c8eaf39

Time (s): cpu = 00:00:08 ; elapsed = 00:00:06 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 1a448aed6

Time (s): cpu = 00:00:08 ; elapsed = 00:00:06 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.5 Fast Optimization
Phase 3.5 Fast Optimization | Checksum: 1d33612cc

Time (s): cpu = 00:00:09 ; elapsed = 00:00:08 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.6 Small Shape Detail Placement
Phase 3.6 Small Shape Detail Placement | Checksum: 21a1c0b06

Time (s): cpu = 00:00:10 ; elapsed = 00:00:08 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.7 Re-assign LUT pins
Phase 3.7 Re-assign LUT pins | Checksum: 1f951da77

Time (s): cpu = 00:00:10 ; elapsed = 00:00:08 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.8 Pipeline Register Optimization
Phase 3.8 Pipeline Register Optimization | Checksum: 209573a89

Time (s): cpu = 00:00:10 ; elapsed = 00:00:08 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 3.9 Fast Optimization
Phase 3.9 Fast Optimization | Checksum: 16c200519

Time (s): cpu = 00:00:13 ; elapsed = 00:00:11 . Memory (MB): peak = 1343.457 ; gain = 46.594
Phase 3 Detail Placement | Checksum: 16c200519

Time (s): cpu = 00:00:13 ; elapsed = 00:00:11 . Memory (MB): peak = 1343.457 ; gain = 46.594

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
Post Placement Optimization Initialization | Checksum: 16d230df4

Phase ******* BUFG Insertion
INFO: [Place 46-46] BUFG insertion identified 0 candidate nets, 0 success, 0 bufg driver replicated, 0 skipped for placement/routing, 0 skipped for timing, 0 skipped for netlist change reason
Phase ******* BUFG Insertion | Checksum: 16d230df4

Time (s): cpu = 00:00:13 ; elapsed = 00:00:12 . Memory (MB): peak = 1353.082 ; gain = 56.219
INFO: [Place 30-746] Post Placement Timing Summary WNS=-13.545. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: 1e1affc1b

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293
Phase 4.1 Post Commit Optimization | Checksum: 1e1affc1b

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 1e1affc1b

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 1e1affc1b

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1353.156 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 17326f267

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 17326f267

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293
Ending Placer Task | Checksum: e939c90c

Time (s): cpu = 00:00:19 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293
INFO: [Common 17-83] Releasing license: Implementation
55 Infos, 0 Warnings, 16 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1353.156 ; gain = 56.293
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1353.156 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1360.730 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.258 . Memory (MB): peak = 1360.781 ; gain = 7.625
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file Top_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.040 . Memory (MB): peak = 1360.781 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file Top_utilization_placed.rpt -pb Top_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file Top_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 1360.781 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: d3c68bf8 ConstDB: 0 ShapeSum: 15733d14 RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 778c87fb

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1514.375 ; gain = 152.188
Post Restoration Checksum: NetGraph: 5937ae20 NumContArr: 1e54d9db Constraints: 0 Timing: 0

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: 778c87fb

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1532.434 ; gain = 170.246

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: 778c87fb

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1538.617 ; gain = 176.430

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: 778c87fb

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1538.617 ; gain = 176.430
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: 201be32bb

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1549.199 ; gain = 187.012
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-13.354| TNS=-375.372| WHS=-0.087 | THS=-2.374 |

Phase 2 Router Initialization | Checksum: 1d23457db

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1557.672 ; gain = 195.484

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: *********

Time (s): cpu = 00:00:20 ; elapsed = 00:00:17 . Memory (MB): peak = 1561.707 ; gain = 199.520

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 2167
 Number of Nodes with overlaps = 864
 Number of Nodes with overlaps = 338
 Number of Nodes with overlaps = 134
 Number of Nodes with overlaps = 83
 Number of Nodes with overlaps = 33
 Number of Nodes with overlaps = 24
 Number of Nodes with overlaps = 12
 Number of Nodes with overlaps = 5
 Number of Nodes with overlaps = 6
 Number of Nodes with overlaps = 3
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-14.961| TNS=-438.633| WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 20ded2309

Time (s): cpu = 00:00:37 ; elapsed = 00:00:30 . Memory (MB): peak = 1561.719 ; gain = 199.531

Phase 4.2 Global Iteration 1
 Number of Nodes with overlaps = 828
 Number of Nodes with overlaps = 440
 Number of Nodes with overlaps = 233
 Number of Nodes with overlaps = 119
 Number of Nodes with overlaps = 62
 Number of Nodes with overlaps = 22
 Number of Nodes with overlaps = 11
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-14.995| TNS=-436.789| WHS=N/A    | THS=N/A    |

Phase 4.2 Global Iteration 1 | Checksum: 11fc1f0ca

Time (s): cpu = 00:00:44 ; elapsed = 00:00:34 . Memory (MB): peak = 1561.719 ; gain = 199.531
Phase 4 Rip-up And Reroute | Checksum: 11fc1f0ca

Time (s): cpu = 00:00:44 ; elapsed = 00:00:34 . Memory (MB): peak = 1561.719 ; gain = 199.531

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp

Phase 5.1.1 Update Timing
Phase 5.1.1 Update Timing | Checksum: 1cef9c44a

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1561.719 ; gain = 199.531
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-14.882| TNS=-436.079| WHS=N/A    | THS=N/A    |

 Number of Nodes with overlaps = 0
Phase 5.1 Delay CleanUp | Checksum: 999ee5a7

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: 999ee5a7

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438
Phase 5 Delay and Skew Optimization | Checksum: 999ee5a7

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 10430159f

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-14.864| TNS=-428.900| WHS=0.212  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: 10430159f

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438
Phase 6 Post Hold Fix | Checksum: 10430159f

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 1.07682 %
  Global Horizontal Routing Utilization  = 1.20894 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Congestion Report
North Dir 1x1 Area, Max Cong = 49.5495%, No Congested Regions.
South Dir 1x1 Area, Max Cong = 68.4685%, No Congested Regions.
East Dir 1x1 Area, Max Cong = 64.7059%, No Congested Regions.
West Dir 1x1 Area, Max Cong = 85.2941%, Congestion bounded by tiles (Lower Left Tile -> Upper Right Tile):
   INT_L_X8Y71 -> INT_L_X8Y71

------------------------------
Reporting congestion hotspots
------------------------------
Direction: North
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: South
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: East
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: West
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 1

Phase 7 Route finalize | Checksum: f3beb5d6

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: f3beb5d6

Time (s): cpu = 00:00:44 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: 17887be0b

Time (s): cpu = 00:00:45 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=-14.864| TNS=-428.900| WHS=0.212  | THS=0.000  |

WARNING: [Route 35-328] Router estimated timing not met.
Resolution: For a complete and accurate timing signoff, report_timing_summary must be run after route_design. Alternatively, route_design can be run with the -timing_summary option to enable a complete timing signoff at the end of route_design.
Phase 10 Post Router Timing | Checksum: 17887be0b

Time (s): cpu = 00:00:45 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:00:45 ; elapsed = 00:00:35 . Memory (MB): peak = 1562.625 ; gain = 200.438

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
73 Infos, 1 Warnings, 16 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:00:46 ; elapsed = 00:00:36 . Memory (MB): peak = 1562.625 ; gain = 201.844
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1562.625 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1562.625 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.290 . Memory (MB): peak = 1562.625 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
Command: report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/Final_pro/pill/pill.runs/impl_1/Top_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file Top_methodology_drc_routed.rpt -pb Top_methodology_drc_routed.pb -rpx Top_methodology_drc_routed.rpx
Command: report_methodology -file Top_methodology_drc_routed.rpt -pb Top_methodology_drc_routed.pb -rpx Top_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file E:/Final_pro/pill/pill.runs/impl_1/Top_methodology_drc_routed.rpt.
report_methodology completed successfully
INFO: [runtcl-4] Executing : report_power -file Top_power_routed.rpt -pb Top_power_summary_routed.pb -rpx Top_power_routed.rpx
Command: report_power -file Top_power_routed.rpt -pb Top_power_summary_routed.pb -rpx Top_power_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
85 Infos, 1 Warnings, 16 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file Top_route_status.rpt -pb Top_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file Top_timing_summary_routed.rpt -pb Top_timing_summary_routed.pb -rpx Top_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
CRITICAL WARNING: [Timing 38-282] The design failed to meet the timing requirements. Please see the timing summary report for details on the timing violations.
INFO: [runtcl-4] Executing : report_incremental_reuse -file Top_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file Top_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file Top_bus_skew_routed.rpt -pb Top_bus_skew_routed.pb -rpx Top_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
Command: write_bitstream -force Top.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC DPIP-1] Input pipelining: DSP data_transform_inst/data0 input data_transform_inst/data0/A[29:0] is not pipelined. Pipelining DSP48 input will improve performance.
WARNING: [DRC DPIP-1] Input pipelining: DSP data_transform_inst/data0 input data_transform_inst/data0/C[47:0] is not pipelined. Pipelining DSP48 input will improve performance.
WARNING: [DRC DPOP-1] PREG Output pipelining: DSP data_transform_inst/data0 output data_transform_inst/data0/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC DPOP-2] MREG Output pipelining: DSP data_transform_inst/data0 multiplier stage data_transform_inst/data0/P[47:0] is not pipelined (MREG=0). Pipelining the multiplier function will improve performance and will save significant power so it is suggested whenever possible to fully pipeline this function.  If this multiplier was inferred, it is suggested to describe an additional register stage after this function.  If there is no registered adder/accumulator following the multiply function, two pipeline stages are suggested to allow both the MREG and PREG registers to be used.  If the DSP48 was instantiated in the design, it is suggested to set both the MREG and PREG attributes to 1 when performing multiply functions.
WARNING: [DRC UCIO-1] Unconstrained Logical Port: 16 out of 41 logical ports have no user assigned specific location constraint (LOC). This may cause I/O contention or incompatibility with the board power or connectivity affecting performance, signal integrity or in extreme cases cause damage to the device or the components to which it is connected. To correct this violation, specify all pin locations. This design will fail to generate a bitstream unless all logical ports have a user specified site LOC constraint defined.  To allow bitstream creation with unspecified pin locations (not recommended), use this command: set_property SEVERITY {Warning} [get_drc_checks UCIO-1].  NOTE: When using the Vivado Runs infrastructure (e.g. launch_runs Tcl command), add this command to a .tcl file and add that file as a pre-hook for write_bitstream step for the implementation run.  Problem ports: col[3:0], row[3:0], h_bw, h_gw, h_qw, h_sw, l_bw, l_gw, l_qw, and l_sw.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 5 Warnings
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Bitstream compression saved 24163328 bits.
Writing bitstream ./Top.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-120] WebTalk data collection is mandatory when using a WebPACK part without a full Vivado license. To see the specific WebTalk data collected for your design, open the usage_statistics_webtalk.html or usage_statistics_webtalk.xml file in the implementation directory.
INFO: [Common 17-83] Releasing license: Implementation
104 Infos, 6 Warnings, 17 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:09 ; elapsed = 00:00:09 . Memory (MB): peak = 2034.105 ; gain = 448.371
INFO: [Common 17-206] Exiting Vivado at Sat May 31 15:19:55 2025...
