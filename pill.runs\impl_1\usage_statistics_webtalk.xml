<?xml version="1.0" encoding="UTF-8" ?>
<webTalkData  fileName='usage_statistics_webtalk.xml'  majorVersion='1' minorVersion='0' timeStamp='Sat May 31 14:46:15 2025'>
<section name="__ROOT__" level="0" order="1" description="">
 <section name="software_version_and_target_device" level="1" order="1" description="">
  <keyValuePair key="beta" value="FALSE" description="" />
  <keyValuePair key="build_version" value="2405991" description="" />
  <keyValuePair key="date_generated" value="Sat May 31 14:46:15 2025" description="" />
  <keyValuePair key="os_platform" value="WIN64" description="" />
  <keyValuePair key="product_version" value="Vivado v2018.3 (64-bit)" description="" />
  <keyValuePair key="project_id" value="9c1e3757b7824518908b7ceb05c83dba" description="" />
  <keyValuePair key="project_iteration" value="3" description="" />
  <keyValuePair key="random_id" value="1df8074c817b5f1cb46b2114e3a8a019" description="" />
  <keyValuePair key="registration_id" value="1df8074c817b5f1cb46b2114e3a8a019" description="" />
  <keyValuePair key="route_design" value="TRUE" description="" />
  <keyValuePair key="target_device" value="xc7a100t" description="" />
  <keyValuePair key="target_family" value="artix7" description="" />
  <keyValuePair key="target_package" value="fgg484" description="" />
  <keyValuePair key="target_speed" value="-1" description="" />
  <keyValuePair key="tool_flow" value="Vivado" description="" />
 </section>
 <section name="user_environment" level="1" order="2" description="">
  <keyValuePair key="cpu_name" value="12th Gen Intel(R) Core(TM) i7-12700H" description="" />
  <keyValuePair key="cpu_speed" value="2688 MHz" description="" />
  <keyValuePair key="os_name" value="Microsoft Windows 8 or later , 64-bit" description="" />
  <keyValuePair key="os_release" value="major release  (build 9200)" description="" />
  <keyValuePair key="system_ram" value="16.000 GB" description="" />
  <keyValuePair key="total_processors" value="1" description="" />
 </section>
 <section name="report_drc" level="1" order="3" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-append" value="default::[not_specified]" description="" />
   <keyValuePair key="-checks" value="default::[not_specified]" description="" />
   <keyValuePair key="-fail_on" value="default::[not_specified]" description="" />
   <keyValuePair key="-force" value="default::[not_specified]" description="" />
   <keyValuePair key="-format" value="default::[not_specified]" description="" />
   <keyValuePair key="-internal" value="default::[not_specified]" description="" />
   <keyValuePair key="-internal_only" value="default::[not_specified]" description="" />
   <keyValuePair key="-messages" value="default::[not_specified]" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_waivers" value="default::[not_specified]" description="" />
   <keyValuePair key="-return_string" value="default::[not_specified]" description="" />
   <keyValuePair key="-ruledecks" value="default::[not_specified]" description="" />
   <keyValuePair key="-upgrade_cw" value="default::[not_specified]" description="" />
   <keyValuePair key="-waived" value="default::[not_specified]" description="" />
  </section>
  <section name="results" level="2" order="2" description="">
   <keyValuePair key="cfgbvs-1" value="1" description="" />
   <keyValuePair key="dpip-1" value="2" description="" />
   <keyValuePair key="dpop-1" value="1" description="" />
   <keyValuePair key="dpop-2" value="1" description="" />
  </section>
 </section>
 <section name="report_methodology" level="1" order="4" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-append" value="default::[not_specified]" description="" />
   <keyValuePair key="-checks" value="default::[not_specified]" description="" />
   <keyValuePair key="-fail_on" value="default::[not_specified]" description="" />
   <keyValuePair key="-force" value="default::[not_specified]" description="" />
   <keyValuePair key="-format" value="default::[not_specified]" description="" />
   <keyValuePair key="-messages" value="default::[not_specified]" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-return_string" value="default::[not_specified]" description="" />
   <keyValuePair key="-slack_lesser_than" value="default::[not_specified]" description="" />
   <keyValuePair key="-waived" value="default::[not_specified]" description="" />
  </section>
  <section name="results" level="2" order="2" description="">
   <keyValuePair key="synth-13" value="1" description="" />
   <keyValuePair key="timing-16" value="46" description="" />
   <keyValuePair key="timing-18" value="40" description="" />
  </section>
 </section>
 <section name="report_power" level="1" order="5" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-advisory" value="default::[not_specified]" description="" />
   <keyValuePair key="-append" value="default::[not_specified]" description="" />
   <keyValuePair key="-file" value="[specified]" description="" />
   <keyValuePair key="-format" value="default::text" description="" />
   <keyValuePair key="-hier" value="default::power" description="" />
   <keyValuePair key="-hierarchical_depth" value="default::4" description="" />
   <keyValuePair key="-l" value="default::[not_specified]" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_propagation" value="default::[not_specified]" description="" />
   <keyValuePair key="-return_string" value="default::[not_specified]" description="" />
   <keyValuePair key="-rpx" value="[specified]" description="" />
   <keyValuePair key="-verbose" value="default::[not_specified]" description="" />
   <keyValuePair key="-vid" value="default::[not_specified]" description="" />
   <keyValuePair key="-xpe" value="default::[not_specified]" description="" />
  </section>
  <section name="usage" level="2" order="2" description="">
   <keyValuePair key="airflow" value="250 (LFM)" description="" />
   <keyValuePair key="ambient_temp" value="25.0 (C)" description="" />
   <keyValuePair key="bi-dir_toggle" value="12.500000" description="" />
   <keyValuePair key="bidir_output_enable" value="1.000000" description="" />
   <keyValuePair key="board_layers" value="12to15 (12 to 15 Layers)" description="" />
   <keyValuePair key="board_selection" value="medium (10&quot;x10&quot;)" description="" />
   <keyValuePair key="clocks" value="0.004876" description="" />
   <keyValuePair key="confidence_level_clock_activity" value="High" description="" />
   <keyValuePair key="confidence_level_design_state" value="High" description="" />
   <keyValuePair key="confidence_level_device_models" value="High" description="" />
   <keyValuePair key="confidence_level_internal_activity" value="Medium" description="" />
   <keyValuePair key="confidence_level_io_activity" value="Low" description="" />
   <keyValuePair key="confidence_level_overall" value="Low" description="" />
   <keyValuePair key="customer" value="TBD" description="" />
   <keyValuePair key="customer_class" value="TBD" description="" />
   <keyValuePair key="devstatic" value="0.097203" description="" />
   <keyValuePair key="die" value="xc7a100tfgg484-1" description="" />
   <keyValuePair key="dsp" value="0.000607" description="" />
   <keyValuePair key="dsp_output_toggle" value="12.500000" description="" />
   <keyValuePair key="dynamic" value="0.109665" description="" />
   <keyValuePair key="effective_thetaja" value="2.7" description="" />
   <keyValuePair key="enable_probability" value="0.990000" description="" />
   <keyValuePair key="family" value="artix7" description="" />
   <keyValuePair key="ff_toggle" value="12.500000" description="" />
   <keyValuePair key="flow_state" value="routed" description="" />
   <keyValuePair key="heatsink" value="medium (Medium Profile)" description="" />
   <keyValuePair key="i/o" value="0.011649" description="" />
   <keyValuePair key="input_toggle" value="12.500000" description="" />
   <keyValuePair key="junction_temp" value="25.6 (C)" description="" />
   <keyValuePair key="logic" value="0.042116" description="" />
   <keyValuePair key="mgtavcc_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="mgtavcc_static_current" value="0.000000" description="" />
   <keyValuePair key="mgtavcc_total_current" value="0.000000" description="" />
   <keyValuePair key="mgtavcc_voltage" value="1.000000" description="" />
   <keyValuePair key="mgtavtt_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="mgtavtt_static_current" value="0.000000" description="" />
   <keyValuePair key="mgtavtt_total_current" value="0.000000" description="" />
   <keyValuePair key="mgtavtt_voltage" value="1.200000" description="" />
   <keyValuePair key="netlist_net_matched" value="NA" description="" />
   <keyValuePair key="off-chip_power" value="0.000000" description="" />
   <keyValuePair key="on-chip_power" value="0.206868" description="" />
   <keyValuePair key="output_enable" value="1.000000" description="" />
   <keyValuePair key="output_load" value="5.000000" description="" />
   <keyValuePair key="output_toggle" value="12.500000" description="" />
   <keyValuePair key="package" value="fgg484" description="" />
   <keyValuePair key="pct_clock_constrained" value="2.000000" description="" />
   <keyValuePair key="pct_inputs_defined" value="10" description="" />
   <keyValuePair key="platform" value="nt64" description="" />
   <keyValuePair key="process" value="typical" description="" />
   <keyValuePair key="ram_enable" value="50.000000" description="" />
   <keyValuePair key="ram_write" value="50.000000" description="" />
   <keyValuePair key="read_saif" value="False" description="" />
   <keyValuePair key="set/reset_probability" value="0.000000" description="" />
   <keyValuePair key="signal_rate" value="False" description="" />
   <keyValuePair key="signals" value="0.050417" description="" />
   <keyValuePair key="simulation_file" value="None" description="" />
   <keyValuePair key="speedgrade" value="-1" description="" />
   <keyValuePair key="static_prob" value="False" description="" />
   <keyValuePair key="temp_grade" value="commercial" description="" />
   <keyValuePair key="thetajb" value="6.8 (C/W)" description="" />
   <keyValuePair key="thetasa" value="4.6 (C/W)" description="" />
   <keyValuePair key="toggle_rate" value="False" description="" />
   <keyValuePair key="user_board_temp" value="25.0 (C)" description="" />
   <keyValuePair key="user_effective_thetaja" value="2.7" description="" />
   <keyValuePair key="user_junc_temp" value="25.6 (C)" description="" />
   <keyValuePair key="user_thetajb" value="6.8 (C/W)" description="" />
   <keyValuePair key="user_thetasa" value="4.6 (C/W)" description="" />
   <keyValuePair key="vccadc_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vccadc_static_current" value="0.020000" description="" />
   <keyValuePair key="vccadc_total_current" value="0.020000" description="" />
   <keyValuePair key="vccadc_voltage" value="1.800000" description="" />
   <keyValuePair key="vccaux_dynamic_current" value="0.000423" description="" />
   <keyValuePair key="vccaux_io_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vccaux_io_static_current" value="0.000000" description="" />
   <keyValuePair key="vccaux_io_total_current" value="0.000000" description="" />
   <keyValuePair key="vccaux_io_voltage" value="1.800000" description="" />
   <keyValuePair key="vccaux_static_current" value="0.018149" description="" />
   <keyValuePair key="vccaux_total_current" value="0.018572" description="" />
   <keyValuePair key="vccaux_voltage" value="1.800000" description="" />
   <keyValuePair key="vccbram_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vccbram_static_current" value="0.000247" description="" />
   <keyValuePair key="vccbram_total_current" value="0.000247" description="" />
   <keyValuePair key="vccbram_voltage" value="1.000000" description="" />
   <keyValuePair key="vccint_dynamic_current" value="0.098113" description="" />
   <keyValuePair key="vccint_static_current" value="0.015089" description="" />
   <keyValuePair key="vccint_total_current" value="0.113201" description="" />
   <keyValuePair key="vccint_voltage" value="1.000000" description="" />
   <keyValuePair key="vcco12_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco12_static_current" value="0.000000" description="" />
   <keyValuePair key="vcco12_total_current" value="0.000000" description="" />
   <keyValuePair key="vcco12_voltage" value="1.200000" description="" />
   <keyValuePair key="vcco135_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco135_static_current" value="0.000000" description="" />
   <keyValuePair key="vcco135_total_current" value="0.000000" description="" />
   <keyValuePair key="vcco135_voltage" value="1.350000" description="" />
   <keyValuePair key="vcco15_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco15_static_current" value="0.000000" description="" />
   <keyValuePair key="vcco15_total_current" value="0.000000" description="" />
   <keyValuePair key="vcco15_voltage" value="1.500000" description="" />
   <keyValuePair key="vcco18_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco18_static_current" value="0.000000" description="" />
   <keyValuePair key="vcco18_total_current" value="0.000000" description="" />
   <keyValuePair key="vcco18_voltage" value="1.800000" description="" />
   <keyValuePair key="vcco25_dynamic_current" value="0.000000" description="" />
   <keyValuePair key="vcco25_static_current" value="0.000000" description="" />
   <keyValuePair key="vcco25_total_current" value="0.000000" description="" />
   <keyValuePair key="vcco25_voltage" value="2.500000" description="" />
   <keyValuePair key="vcco33_dynamic_current" value="0.003270" description="" />
   <keyValuePair key="vcco33_static_current" value="0.004000" description="" />
   <keyValuePair key="vcco33_total_current" value="0.007270" description="" />
   <keyValuePair key="vcco33_voltage" value="3.300000" description="" />
   <keyValuePair key="version" value="2018.3" description="" />
  </section>
 </section>
 <section name="report_utilization" level="1" order="6" description="">
  <section name="clocking" level="2" order="1" description="">
   <keyValuePair key="bufgctrl_available" value="32" description="" />
   <keyValuePair key="bufgctrl_fixed" value="0" description="" />
   <keyValuePair key="bufgctrl_used" value="1" description="" />
   <keyValuePair key="bufgctrl_util_percentage" value="3.13" description="" />
   <keyValuePair key="bufhce_available" value="96" description="" />
   <keyValuePair key="bufhce_fixed" value="0" description="" />
   <keyValuePair key="bufhce_used" value="0" description="" />
   <keyValuePair key="bufhce_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufio_available" value="24" description="" />
   <keyValuePair key="bufio_fixed" value="0" description="" />
   <keyValuePair key="bufio_used" value="0" description="" />
   <keyValuePair key="bufio_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufmrce_available" value="12" description="" />
   <keyValuePair key="bufmrce_fixed" value="0" description="" />
   <keyValuePair key="bufmrce_used" value="0" description="" />
   <keyValuePair key="bufmrce_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufr_available" value="24" description="" />
   <keyValuePair key="bufr_fixed" value="0" description="" />
   <keyValuePair key="bufr_used" value="0" description="" />
   <keyValuePair key="bufr_util_percentage" value="0.00" description="" />
   <keyValuePair key="mmcme2_adv_available" value="6" description="" />
   <keyValuePair key="mmcme2_adv_fixed" value="0" description="" />
   <keyValuePair key="mmcme2_adv_used" value="0" description="" />
   <keyValuePair key="mmcme2_adv_util_percentage" value="0.00" description="" />
   <keyValuePair key="plle2_adv_available" value="6" description="" />
   <keyValuePair key="plle2_adv_fixed" value="0" description="" />
   <keyValuePair key="plle2_adv_used" value="0" description="" />
   <keyValuePair key="plle2_adv_util_percentage" value="0.00" description="" />
  </section>
  <section name="dsp" level="2" order="2" description="">
   <keyValuePair key="dsp48e1_only_used" value="1" description="" />
   <keyValuePair key="dsps_available" value="240" description="" />
   <keyValuePair key="dsps_fixed" value="0" description="" />
   <keyValuePair key="dsps_used" value="1" description="" />
   <keyValuePair key="dsps_util_percentage" value="0.42" description="" />
  </section>
  <section name="io_standard" level="2" order="3" description="">
   <keyValuePair key="blvds_25" value="0" description="" />
   <keyValuePair key="diff_hstl_i" value="0" description="" />
   <keyValuePair key="diff_hstl_i_18" value="0" description="" />
   <keyValuePair key="diff_hstl_ii" value="0" description="" />
   <keyValuePair key="diff_hstl_ii_18" value="0" description="" />
   <keyValuePair key="diff_hsul_12" value="0" description="" />
   <keyValuePair key="diff_mobile_ddr" value="0" description="" />
   <keyValuePair key="diff_sstl135" value="0" description="" />
   <keyValuePair key="diff_sstl135_r" value="0" description="" />
   <keyValuePair key="diff_sstl15" value="0" description="" />
   <keyValuePair key="diff_sstl15_r" value="0" description="" />
   <keyValuePair key="diff_sstl18_i" value="0" description="" />
   <keyValuePair key="diff_sstl18_ii" value="0" description="" />
   <keyValuePair key="hstl_i" value="0" description="" />
   <keyValuePair key="hstl_i_18" value="0" description="" />
   <keyValuePair key="hstl_ii" value="0" description="" />
   <keyValuePair key="hstl_ii_18" value="0" description="" />
   <keyValuePair key="hsul_12" value="0" description="" />
   <keyValuePair key="lvcmos12" value="0" description="" />
   <keyValuePair key="lvcmos15" value="0" description="" />
   <keyValuePair key="lvcmos18" value="0" description="" />
   <keyValuePair key="lvcmos25" value="0" description="" />
   <keyValuePair key="lvcmos33" value="1" description="" />
   <keyValuePair key="lvds_25" value="0" description="" />
   <keyValuePair key="lvttl" value="0" description="" />
   <keyValuePair key="mini_lvds_25" value="0" description="" />
   <keyValuePair key="mobile_ddr" value="0" description="" />
   <keyValuePair key="pci33_3" value="0" description="" />
   <keyValuePair key="ppds_25" value="0" description="" />
   <keyValuePair key="rsds_25" value="0" description="" />
   <keyValuePair key="sstl135" value="0" description="" />
   <keyValuePair key="sstl135_r" value="0" description="" />
   <keyValuePair key="sstl15" value="0" description="" />
   <keyValuePair key="sstl15_r" value="0" description="" />
   <keyValuePair key="sstl18_i" value="0" description="" />
   <keyValuePair key="sstl18_ii" value="0" description="" />
   <keyValuePair key="tmds_33" value="0" description="" />
  </section>
  <section name="memory" level="2" order="4" description="">
   <keyValuePair key="block_ram_tile_available" value="135" description="" />
   <keyValuePair key="block_ram_tile_fixed" value="0" description="" />
   <keyValuePair key="block_ram_tile_used" value="0" description="" />
   <keyValuePair key="block_ram_tile_util_percentage" value="0.00" description="" />
   <keyValuePair key="ramb18_available" value="270" description="" />
   <keyValuePair key="ramb18_fixed" value="0" description="" />
   <keyValuePair key="ramb18_used" value="0" description="" />
   <keyValuePair key="ramb18_util_percentage" value="0.00" description="" />
   <keyValuePair key="ramb36_fifo_available" value="135" description="" />
   <keyValuePair key="ramb36_fifo_fixed" value="0" description="" />
   <keyValuePair key="ramb36_fifo_used" value="0" description="" />
   <keyValuePair key="ramb36_fifo_util_percentage" value="0.00" description="" />
  </section>
  <section name="primitives" level="2" order="5" description="">
   <keyValuePair key="bufg_functional_category" value="Clock" description="" />
   <keyValuePair key="bufg_used" value="1" description="" />
   <keyValuePair key="carry4_functional_category" value="CarryLogic" description="" />
   <keyValuePair key="carry4_used" value="641" description="" />
   <keyValuePair key="dsp48e1_functional_category" value="Block Arithmetic" description="" />
   <keyValuePair key="dsp48e1_used" value="1" description="" />
   <keyValuePair key="fdce_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdce_used" value="315" description="" />
   <keyValuePair key="fdpe_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdpe_used" value="15" description="" />
   <keyValuePair key="fdre_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdre_used" value="54" description="" />
   <keyValuePair key="ibuf_functional_category" value="IO" description="" />
   <keyValuePair key="ibuf_used" value="10" description="" />
   <keyValuePair key="lut1_functional_category" value="LUT" description="" />
   <keyValuePair key="lut1_used" value="87" description="" />
   <keyValuePair key="lut2_functional_category" value="LUT" description="" />
   <keyValuePair key="lut2_used" value="797" description="" />
   <keyValuePair key="lut3_functional_category" value="LUT" description="" />
   <keyValuePair key="lut3_used" value="883" description="" />
   <keyValuePair key="lut4_functional_category" value="LUT" description="" />
   <keyValuePair key="lut4_used" value="736" description="" />
   <keyValuePair key="lut5_functional_category" value="LUT" description="" />
   <keyValuePair key="lut5_used" value="308" description="" />
   <keyValuePair key="lut6_functional_category" value="LUT" description="" />
   <keyValuePair key="lut6_used" value="1241" description="" />
   <keyValuePair key="muxf7_functional_category" value="MuxFx" description="" />
   <keyValuePair key="muxf7_used" value="4" description="" />
   <keyValuePair key="obuf_functional_category" value="IO" description="" />
   <keyValuePair key="obuf_used" value="31" description="" />
  </section>
  <section name="slice_logic" level="2" order="6" description="">
   <keyValuePair key="f7_muxes_available" value="31700" description="" />
   <keyValuePair key="f7_muxes_fixed" value="0" description="" />
   <keyValuePair key="f7_muxes_used" value="4" description="" />
   <keyValuePair key="f7_muxes_util_percentage" value="0.01" description="" />
   <keyValuePair key="f8_muxes_available" value="15850" description="" />
   <keyValuePair key="f8_muxes_fixed" value="0" description="" />
   <keyValuePair key="f8_muxes_used" value="0" description="" />
   <keyValuePair key="f8_muxes_util_percentage" value="0.00" description="" />
   <keyValuePair key="lut_as_distributed_ram_fixed" value="0" description="" />
   <keyValuePair key="lut_as_distributed_ram_used" value="0" description="" />
   <keyValuePair key="lut_as_logic_available" value="63400" description="" />
   <keyValuePair key="lut_as_logic_available" value="63400" description="" />
   <keyValuePair key="lut_as_logic_fixed" value="0" description="" />
   <keyValuePair key="lut_as_logic_fixed" value="0" description="" />
   <keyValuePair key="lut_as_logic_used" value="3359" description="" />
   <keyValuePair key="lut_as_logic_used" value="3359" description="" />
   <keyValuePair key="lut_as_logic_util_percentage" value="5.30" description="" />
   <keyValuePair key="lut_as_logic_util_percentage" value="5.30" description="" />
   <keyValuePair key="lut_as_memory_available" value="19000" description="" />
   <keyValuePair key="lut_as_memory_available" value="19000" description="" />
   <keyValuePair key="lut_as_memory_fixed" value="0" description="" />
   <keyValuePair key="lut_as_memory_fixed" value="0" description="" />
   <keyValuePair key="lut_as_memory_used" value="0" description="" />
   <keyValuePair key="lut_as_memory_used" value="0" description="" />
   <keyValuePair key="lut_as_memory_util_percentage" value="0.00" description="" />
   <keyValuePair key="lut_as_memory_util_percentage" value="0.00" description="" />
   <keyValuePair key="lut_as_shift_register_fixed" value="0" description="" />
   <keyValuePair key="lut_as_shift_register_used" value="0" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_unused_fixed" value="0" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_unused_used" value="16" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_used_fixed" value="16" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_used_used" value="37" description="" />
   <keyValuePair key="register_as_flip_flop_available" value="126800" description="" />
   <keyValuePair key="register_as_flip_flop_fixed" value="0" description="" />
   <keyValuePair key="register_as_flip_flop_used" value="384" description="" />
   <keyValuePair key="register_as_flip_flop_util_percentage" value="0.30" description="" />
   <keyValuePair key="register_as_latch_available" value="126800" description="" />
   <keyValuePair key="register_as_latch_fixed" value="0" description="" />
   <keyValuePair key="register_as_latch_used" value="0" description="" />
   <keyValuePair key="register_as_latch_util_percentage" value="0.00" description="" />
   <keyValuePair key="register_driven_from_outside_the_slice_fixed" value="37" description="" />
   <keyValuePair key="register_driven_from_outside_the_slice_used" value="53" description="" />
   <keyValuePair key="register_driven_from_within_the_slice_fixed" value="53" description="" />
   <keyValuePair key="register_driven_from_within_the_slice_used" value="331" description="" />
   <keyValuePair key="slice_available" value="15850" description="" />
   <keyValuePair key="slice_fixed" value="0" description="" />
   <keyValuePair key="slice_luts_available" value="63400" description="" />
   <keyValuePair key="slice_luts_fixed" value="0" description="" />
   <keyValuePair key="slice_luts_used" value="3359" description="" />
   <keyValuePair key="slice_luts_util_percentage" value="5.30" description="" />
   <keyValuePair key="slice_registers_available" value="126800" description="" />
   <keyValuePair key="slice_registers_available" value="126800" description="" />
   <keyValuePair key="slice_registers_fixed" value="0" description="" />
   <keyValuePair key="slice_registers_fixed" value="0" description="" />
   <keyValuePair key="slice_registers_used" value="384" description="" />
   <keyValuePair key="slice_registers_used" value="384" description="" />
   <keyValuePair key="slice_registers_util_percentage" value="0.30" description="" />
   <keyValuePair key="slice_registers_util_percentage" value="0.30" description="" />
   <keyValuePair key="slice_used" value="1088" description="" />
   <keyValuePair key="slice_util_percentage" value="6.86" description="" />
   <keyValuePair key="slicel_fixed" value="0" description="" />
   <keyValuePair key="slicel_used" value="754" description="" />
   <keyValuePair key="slicem_fixed" value="0" description="" />
   <keyValuePair key="slicem_used" value="334" description="" />
   <keyValuePair key="unique_control_sets_available" value="15850" description="" />
   <keyValuePair key="unique_control_sets_fixed" value="15850" description="" />
   <keyValuePair key="unique_control_sets_used" value="27" description="" />
   <keyValuePair key="unique_control_sets_util_percentage" value="0.17" description="" />
   <keyValuePair key="using_o5_and_o6_fixed" value="0.17" description="" />
   <keyValuePair key="using_o5_and_o6_used" value="693" description="" />
   <keyValuePair key="using_o5_output_only_fixed" value="693" description="" />
   <keyValuePair key="using_o5_output_only_used" value="2" description="" />
   <keyValuePair key="using_o6_output_only_fixed" value="2" description="" />
   <keyValuePair key="using_o6_output_only_used" value="2664" description="" />
  </section>
  <section name="specific_feature" level="2" order="7" description="">
   <keyValuePair key="bscane2_available" value="4" description="" />
   <keyValuePair key="bscane2_fixed" value="0" description="" />
   <keyValuePair key="bscane2_used" value="0" description="" />
   <keyValuePair key="bscane2_util_percentage" value="0.00" description="" />
   <keyValuePair key="capturee2_available" value="1" description="" />
   <keyValuePair key="capturee2_fixed" value="0" description="" />
   <keyValuePair key="capturee2_used" value="0" description="" />
   <keyValuePair key="capturee2_util_percentage" value="0.00" description="" />
   <keyValuePair key="dna_port_available" value="1" description="" />
   <keyValuePair key="dna_port_fixed" value="0" description="" />
   <keyValuePair key="dna_port_used" value="0" description="" />
   <keyValuePair key="dna_port_util_percentage" value="0.00" description="" />
   <keyValuePair key="efuse_usr_available" value="1" description="" />
   <keyValuePair key="efuse_usr_fixed" value="0" description="" />
   <keyValuePair key="efuse_usr_used" value="0" description="" />
   <keyValuePair key="efuse_usr_util_percentage" value="0.00" description="" />
   <keyValuePair key="frame_ecce2_available" value="1" description="" />
   <keyValuePair key="frame_ecce2_fixed" value="0" description="" />
   <keyValuePair key="frame_ecce2_used" value="0" description="" />
   <keyValuePair key="frame_ecce2_util_percentage" value="0.00" description="" />
   <keyValuePair key="icape2_available" value="2" description="" />
   <keyValuePair key="icape2_fixed" value="0" description="" />
   <keyValuePair key="icape2_used" value="0" description="" />
   <keyValuePair key="icape2_util_percentage" value="0.00" description="" />
   <keyValuePair key="pcie_2_1_available" value="1" description="" />
   <keyValuePair key="pcie_2_1_fixed" value="0" description="" />
   <keyValuePair key="pcie_2_1_used" value="0" description="" />
   <keyValuePair key="pcie_2_1_util_percentage" value="0.00" description="" />
   <keyValuePair key="startupe2_available" value="1" description="" />
   <keyValuePair key="startupe2_fixed" value="0" description="" />
   <keyValuePair key="startupe2_used" value="0" description="" />
   <keyValuePair key="startupe2_util_percentage" value="0.00" description="" />
   <keyValuePair key="xadc_available" value="1" description="" />
   <keyValuePair key="xadc_fixed" value="0" description="" />
   <keyValuePair key="xadc_used" value="0" description="" />
   <keyValuePair key="xadc_util_percentage" value="0.00" description="" />
  </section>
 </section>
 <section name="synthesis" level="1" order="7" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-assert" value="default::[not_specified]" description="" />
   <keyValuePair key="-bufg" value="default::12" description="" />
   <keyValuePair key="-cascade_dsp" value="default::auto" description="" />
   <keyValuePair key="-constrset" value="default::[not_specified]" description="" />
   <keyValuePair key="-control_set_opt_threshold" value="default::auto" description="" />
   <keyValuePair key="-directive" value="default::default" description="" />
   <keyValuePair key="-fanout_limit" value="default::10000" description="" />
   <keyValuePair key="-flatten_hierarchy" value="default::rebuilt" description="" />
   <keyValuePair key="-fsm_extraction" value="default::auto" description="" />
   <keyValuePair key="-gated_clock_conversion" value="default::off" description="" />
   <keyValuePair key="-generic" value="default::[not_specified]" description="" />
   <keyValuePair key="-include_dirs" value="default::[not_specified]" description="" />
   <keyValuePair key="-keep_equivalent_registers" value="default::[not_specified]" description="" />
   <keyValuePair key="-max_bram" value="default::-1" description="" />
   <keyValuePair key="-max_bram_cascade_height" value="default::-1" description="" />
   <keyValuePair key="-max_dsp" value="default::-1" description="" />
   <keyValuePair key="-max_uram" value="default::-1" description="" />
   <keyValuePair key="-max_uram_cascade_height" value="default::-1" description="" />
   <keyValuePair key="-mode" value="default::default" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_lc" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_srlextract" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_timing_driven" value="default::[not_specified]" description="" />
   <keyValuePair key="-part" value="xc7a100tfgg484-1" description="" />
   <keyValuePair key="-resource_sharing" value="default::auto" description="" />
   <keyValuePair key="-retiming" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl_skip_constraints" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl_skip_ip" value="default::[not_specified]" description="" />
   <keyValuePair key="-seu_protect" value="default::none" description="" />
   <keyValuePair key="-sfcu" value="default::[not_specified]" description="" />
   <keyValuePair key="-shreg_min_size" value="default::3" description="" />
   <keyValuePair key="-top" value="Top" description="" />
   <keyValuePair key="-verilog_define" value="default::[not_specified]" description="" />
  </section>
  <section name="usage" level="2" order="2" description="">
   <keyValuePair key="elapsed" value="00:00:47s" description="" />
   <keyValuePair key="hls_ip" value="0" description="" />
   <keyValuePair key="memory_gain" value="853.020MB" description="" />
   <keyValuePair key="memory_peak" value="1173.918MB" description="" />
  </section>
 </section>
 <section name="unisim_transformation" level="1" order="8" description="">
  <section name="post_unisim_transformation" level="2" order="1" description="">
   <keyValuePair key="bufg" value="1" description="" />
   <keyValuePair key="carry4" value="641" description="" />
   <keyValuePair key="dsp48e1" value="1" description="" />
   <keyValuePair key="fdce" value="315" description="" />
   <keyValuePair key="fdpe" value="15" description="" />
   <keyValuePair key="fdre" value="54" description="" />
   <keyValuePair key="gnd" value="7" description="" />
   <keyValuePair key="ibuf" value="10" description="" />
   <keyValuePair key="lut1" value="87" description="" />
   <keyValuePair key="lut2" value="797" description="" />
   <keyValuePair key="lut3" value="883" description="" />
   <keyValuePair key="lut4" value="736" description="" />
   <keyValuePair key="lut5" value="308" description="" />
   <keyValuePair key="lut6" value="1241" description="" />
   <keyValuePair key="muxf7" value="4" description="" />
   <keyValuePair key="obuf" value="31" description="" />
   <keyValuePair key="vcc" value="6" description="" />
  </section>
  <section name="pre_unisim_transformation" level="2" order="2" description="">
   <keyValuePair key="bufg" value="1" description="" />
   <keyValuePair key="carry4" value="641" description="" />
   <keyValuePair key="dsp48e1" value="1" description="" />
   <keyValuePair key="fdce" value="315" description="" />
   <keyValuePair key="fdpe" value="15" description="" />
   <keyValuePair key="fdre" value="54" description="" />
   <keyValuePair key="gnd" value="7" description="" />
   <keyValuePair key="ibuf" value="10" description="" />
   <keyValuePair key="lut1" value="87" description="" />
   <keyValuePair key="lut2" value="797" description="" />
   <keyValuePair key="lut3" value="883" description="" />
   <keyValuePair key="lut4" value="736" description="" />
   <keyValuePair key="lut5" value="308" description="" />
   <keyValuePair key="lut6" value="1241" description="" />
   <keyValuePair key="muxf7" value="4" description="" />
   <keyValuePair key="obuf" value="31" description="" />
   <keyValuePair key="vcc" value="6" description="" />
  </section>
 </section>
 <section name="vivado_usage" level="1" order="9" description="">
  <section name="gui_handlers" level="2" order="1" description="">
   <keyValuePair key="abstractfileview_reload" value="1" description="" />
   <keyValuePair key="addsrcwizard_specify_or_create_constraint_files" value="1" description="" />
   <keyValuePair key="basedialog_ok" value="28" description="" />
   <keyValuePair key="basedialog_yes" value="10" description="" />
   <keyValuePair key="closeplanner_yes" value="1" description="" />
   <keyValuePair key="cmdmsgdialog_ok" value="3" description="" />
   <keyValuePair key="constraintschooserpanel_add_existing_or_create_new_constraints" value="1" description="" />
   <keyValuePair key="constraintschooserpanel_add_files" value="1" description="" />
   <keyValuePair key="createconstraintsfilepanel_file_name" value="1" description="" />
   <keyValuePair key="filesetpanel_file_set_panel_tree" value="14" description="" />
   <keyValuePair key="flownavigatortreepanel_flow_navigator_tree" value="27" description="" />
   <keyValuePair key="hardwaretreepanel_hardware_tree_table" value="2" description="" />
   <keyValuePair key="msgtreepanel_message_severity" value="3" description="" />
   <keyValuePair key="msgtreepanel_message_view_tree" value="6" description="" />
   <keyValuePair key="msgview_critical_warnings" value="4" description="" />
   <keyValuePair key="overwriteconstraintsdialog_overwrite" value="1" description="" />
   <keyValuePair key="pacommandnames_auto_connect_target" value="1" description="" />
   <keyValuePair key="pacommandnames_auto_update_hier" value="2" description="" />
   <keyValuePair key="pacommandnames_goto_implemented_design" value="1" description="" />
   <keyValuePair key="paviews_project_summary" value="8" description="" />
   <keyValuePair key="programdebugtab_refresh_device" value="1" description="" />
   <keyValuePair key="programfpgadialog_program" value="2" description="" />
   <keyValuePair key="projecttab_reload" value="1" description="" />
   <keyValuePair key="rdicommands_delete" value="1" description="" />
   <keyValuePair key="saveprojectutils_reload" value="1" description="" />
   <keyValuePair key="saveprojectutils_save" value="2" description="" />
   <keyValuePair key="signaltreepanel_signal_tree_table" value="186" description="" />
   <keyValuePair key="srcchooserpanel_add_hdl_and_netlist_files_to_your_project" value="1" description="" />
   <keyValuePair key="srcchooserpanel_add_or_create_source_file" value="1" description="" />
   <keyValuePair key="srcmenu_ip_hierarchy" value="2" description="" />
   <keyValuePair key="stalerundialog_yes" value="1" description="" />
  </section>
  <section name="java_command_handlers" level="2" order="2" description="">
   <keyValuePair key="addsources" value="2" description="" />
   <keyValuePair key="autoconnecttarget" value="1" description="" />
   <keyValuePair key="editdelete" value="1" description="" />
   <keyValuePair key="launchprogramfpga" value="2" description="" />
   <keyValuePair key="newhardwaredashboard" value="1" description="" />
   <keyValuePair key="openhardwaremanager" value="4" description="" />
   <keyValuePair key="openrecenttarget" value="2" description="" />
   <keyValuePair key="programdevice" value="2" description="" />
   <keyValuePair key="refreshdevice" value="1" description="" />
   <keyValuePair key="runbitgen" value="9" description="" />
   <keyValuePair key="runimplementation" value="4" description="" />
   <keyValuePair key="savedesign" value="4" description="" />
   <keyValuePair key="showview" value="6" description="" />
   <keyValuePair key="timingconstraintswizard" value="1" description="" />
   <keyValuePair key="viewtaskimplementation" value="5" description="" />
   <keyValuePair key="viewtaskprojectmanager" value="1" description="" />
   <keyValuePair key="viewtasksynthesis" value="2" description="" />
  </section>
  <section name="other_data" level="2" order="3" description="">
   <keyValuePair key="guimode" value="1" description="" />
  </section>
  <section name="project_data" level="2" order="4" description="">
   <keyValuePair key="constraintsetcount" value="1" description="" />
   <keyValuePair key="core_container" value="false" description="" />
   <keyValuePair key="currentimplrun" value="impl_1" description="" />
   <keyValuePair key="currentsynthesisrun" value="synth_1" description="" />
   <keyValuePair key="default_library" value="xil_defaultlib" description="" />
   <keyValuePair key="designmode" value="RTL" description="" />
   <keyValuePair key="export_simulation_activehdl" value="0" description="" />
   <keyValuePair key="export_simulation_ies" value="0" description="" />
   <keyValuePair key="export_simulation_modelsim" value="0" description="" />
   <keyValuePair key="export_simulation_questa" value="0" description="" />
   <keyValuePair key="export_simulation_riviera" value="0" description="" />
   <keyValuePair key="export_simulation_vcs" value="0" description="" />
   <keyValuePair key="export_simulation_xsim" value="0" description="" />
   <keyValuePair key="implstrategy" value="Vivado Implementation Defaults" description="" />
   <keyValuePair key="launch_simulation_activehdl" value="0" description="" />
   <keyValuePair key="launch_simulation_ies" value="0" description="" />
   <keyValuePair key="launch_simulation_modelsim" value="0" description="" />
   <keyValuePair key="launch_simulation_questa" value="0" description="" />
   <keyValuePair key="launch_simulation_riviera" value="0" description="" />
   <keyValuePair key="launch_simulation_vcs" value="0" description="" />
   <keyValuePair key="launch_simulation_xsim" value="0" description="" />
   <keyValuePair key="simulator_language" value="Mixed" description="" />
   <keyValuePair key="srcsetcount" value="7" description="" />
   <keyValuePair key="synthesisstrategy" value="Vivado Synthesis Defaults" description="" />
   <keyValuePair key="target_language" value="Verilog" description="" />
   <keyValuePair key="target_simulator" value="XSim" description="" />
   <keyValuePair key="totalimplruns" value="1" description="" />
   <keyValuePair key="totalsynthesisruns" value="1" description="" />
  </section>
 </section>
</section>
</webTalkData>
