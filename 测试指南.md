# 药片装瓶系统测试指南

## 修复完成确认

✅ **已完成的修复**：
- keyboard.v - 修正case语句，优化消抖逻辑
- main.v - 修正状态机逻辑，修正复位优先级  
- light_control.v - 修正case语句，优化LED控制
- constraint.xdc - 添加上拉电阻，优化引脚配置
- 原始文件已备份到：`E:\vsPro\BUPT_Digital_Logic-main\backup_20250531_150224`

## 下一步操作流程

### 1. 重新综合项目

1. **打开Vivado**
   - 打开项目文件：`E:\Final_pro\pill\pill.xpr`

2. **运行综合**
   ```
   Flow Navigator -> Synthesis -> Run Synthesis
   ```

3. **检查综合报告**
   - 查看 `Synthesis Completed` 状态
   - 检查是否还有以下警告：
     - ❌ `case statement is not full and has no default`
     - ❌ `Register xxx has both Set and reset with same priority`
   - 这些警告应该已经消除

### 2. 运行实现

1. **运行实现**
   ```
   Flow Navigator -> Implementation -> Run Implementation
   ```

2. **检查实现报告**
   - 确认没有严重错误
   - 检查时序约束是否满足

### 3. 生成比特流

1. **生成比特流**
   ```
   Flow Navigator -> Program and Debug -> Generate Bitstream
   ```

2. **等待完成**
   - 确认比特流生成成功

### 4. 下载到FPGA

1. **连接FPGA板**
   - 确保USB线连接正常
   - 确保板子供电

2. **下载比特流**
   ```
   Flow Navigator -> Program and Debug -> Open Hardware Manager
   -> Open Target -> Auto Connect
   -> Program Device
   ```

## 功能测试清单

### 测试1：系统初始状态
- [ ] **上电后LED状态正常**
  - 最左侧LED不应该异常亮起
  - 所有LED应处于正确初始状态

### 测试2：复位功能
- [ ] **按下S6复位按钮**
  - 数码管应显示 `0000 0000`
  - 黄色LED应亮起（表示设置状态）
  - 系统进入设置模式

### 测试3：键盘输入功能
- [ ] **测试数字键1-9**
  - 按下数字键1：数码管应显示 `0000 0001`
  - 按下数字键2：数码管应显示 `0000 0012`
  - 按下数字键3：数码管应显示 `0000 0123`
  - 继续测试其他数字键

- [ ] **测试ACK确认键**
  - 输入数字后按ACK：应确认输入并进入下一设置步骤
  - 黄色LED状态应改变

### 测试4：系统状态转换
- [ ] **设置最大瓶数**
  - 输入数字（如100）
  - 按ACK确认
  - 黄色LED应变为单个LED亮起

- [ ] **设置单瓶药片数**
  - 输入数字（如50）
  - 按ACK确认
  - 黄色LED应熄灭，系统进入工作准备状态

### 测试5：工作模式
- [ ] **启动工作模式**
  - 拨动START开关到高电平
  - 绿色LED应开始流水灯效果
  - 数码管应显示当前工作进度

### 测试6：显示模式切换
- [ ] **切换显示模式**
  - 拨动display_mode开关
  - 数码管显示内容应在不同模式间切换

## 故障排除

### 如果键盘仍然无响应

1. **检查约束文件**
   - 确认键盘引脚分配正确
   - 确认上拉电阻已添加

2. **检查硬件连接**
   - 确认键盘排线连接正确
   - 检查引脚是否有短路或断路

3. **使用ILA调试**
   - 添加ILA核心监控键盘信号
   - 观察row和col信号变化

### 如果LED显示异常

1. **检查light_control模块**
   - 确认case语句已修正
   - 检查LED控制逻辑

2. **检查约束文件**
   - 确认LED引脚分配正确

### 如果数码管显示异常

1. **检查nixie_tube模块**
   - 确认数码管驱动逻辑正确

2. **检查data_transform模块**
   - 确认数据转换逻辑正确

## 预期测试结果

### 正常工作流程
1. **上电** → 所有LED处于正确初始状态
2. **复位** → 进入设置模式，黄色LED指示
3. **输入数字** → 数码管实时显示输入值
4. **确认设置** → 系统状态正常切换
5. **启动工作** → 绿色LED流水灯，系统正常工作

### 成功标志
- ✅ 键盘响应正常
- ✅ 数码管显示正确
- ✅ LED状态指示准确
- ✅ 系统状态机正常切换
- ✅ 无异常LED亮起

## 联系支持

如果在测试过程中遇到问题：
1. 记录具体的错误现象
2. 保存综合和实现报告
3. 检查硬件连接
4. 参考备份文件进行对比

**备份位置**：`E:\vsPro\BUPT_Digital_Logic-main\backup_20250531_150224`
