Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat May 31 15:19:08 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_control_sets -verbose -file Top_control_sets_placed.rpt
| Design       : Top
| Device       : xc7a100t
------------------------------------------------------------------------------------

Control Set Information

Table of Contents
-----------------
1. Summary
2. Histogram
3. Flip-Flop Distribution
4. Detailed Control Set Information

1. Summary
----------

+----------------------------------------------------------+-------+
|                          Status                          | Count |
+----------------------------------------------------------+-------+
| Number of unique control sets                            |    21 |
| Unused register locations in slices containing registers |    68 |
+----------------------------------------------------------+-------+


2. Histogram
------------

+--------+--------------+
| Fanout | Control Sets |
+--------+--------------+
|      2 |            1 |
|      4 |            3 |
|      6 |            1 |
|      7 |            1 |
|      8 |            1 |
|     14 |            5 |
|     15 |            1 |
|    16+ |            8 |
+--------+--------------+


3. Flip-Flop Distribution
-------------------------

+--------------+-----------------------+------------------------+-----------------+--------------+
| Clock Enable | Synchronous Set/Reset | Asynchronous Set/Reset | Total Registers | Total Slices |
+--------------+-----------------------+------------------------+-----------------+--------------+
| No           | No                    | No                     |              28 |           21 |
| No           | No                    | Yes                    |             114 |           58 |
| No           | Yes                   | No                     |               0 |            0 |
| Yes          | No                    | No                     |               0 |            0 |
| Yes          | No                    | Yes                    |             238 |           68 |
| Yes          | Yes                   | No                     |               0 |            0 |
+--------------+-----------------------+------------------------+-----------------+--------------+


4. Detailed Control Set Information
-----------------------------------

+--------------------+--------------------------------------------+-------------------------------------------+------------------+----------------+
|    Clock Signal    |                Enable Signal               |              Set/Reset Signal             | Slice Load Count | Bel Load Count |
+--------------------+--------------------------------------------+-------------------------------------------+------------------+----------------+
|  sys_clk_IBUF_BUFG |                                            | light_control_inst/sys_rst_n              |                1 |              2 |
|  sys_clk_IBUF_BUFG | main_inst/E[0]                             | main_inst/sys_rst_n                       |                2 |              4 |
|  sys_clk_IBUF_BUFG | keyboard_inst/char[3]_i_1_n_0              | main_inst/sys_rst_n                       |                2 |              4 |
|  sys_clk_IBUF_BUFG | keyboard_inst/row[3]_i_1_n_0               | main_inst/sys_rst_n                       |                1 |              4 |
|  sys_clk_IBUF_BUFG |                                            | keyboard_inst/FSM_onehot_state[2]_i_2_n_0 |                3 |              6 |
|  sys_clk_IBUF_BUFG | nixie_tube_inst/smg[6]_i_1_n_0             | light_control_inst/sys_rst_n              |                1 |              7 |
|  sys_clk_IBUF_BUFG | main_inst/E[0]                             | light_control_inst/sys_rst_n              |                2 |              8 |
|  sys_clk_IBUF_BUFG | main_inst/bot_finished[0]_i_1_n_0          | main_inst/sys_rst_n                       |                4 |             14 |
|  sys_clk_IBUF_BUFG | main_inst/max_bot_num[13]_i_1_n_0          | main_inst/sys_rst_n                       |                4 |             14 |
|  sys_clk_IBUF_BUFG | main_inst/max_sgl_bot[13]_i_1_n_0          | main_inst/sys_rst_n                       |                8 |             14 |
|  sys_clk_IBUF_BUFG | main_inst/now_bot_bil_num[13]_i_1_n_0      | main_inst/sys_rst_n                       |                5 |             14 |
|  sys_clk_IBUF_BUFG | keyboard_inst/temp[13]_i_1_n_0             | main_inst/sys_rst_n                       |                2 |             14 |
|  sys_clk_IBUF_BUFG | nixie_tube_inst/p_0_in                     | nixie_tube_inst/sys_rst_n                 |                5 |             15 |
|  sys_clk_IBUF_BUFG | keyboard_inst/cnt[0]_i_1_n_0               | keyboard_inst/FSM_onehot_state[2]_i_2_n_0 |                6 |             22 |
|  sys_clk_IBUF_BUFG | keyboard_inst/debounce_cnt_0               | keyboard_inst/FSM_onehot_state[2]_i_2_n_0 |                6 |             25 |
|  sys_clk_IBUF_BUFG | main_inst/ack_debounce_cnt_1               | main_inst/sys_rst_n                       |                5 |             26 |
|  sys_clk_IBUF_BUFG | keyboard_inst/ack_debounce_cnt[25]_i_1_n_0 | keyboard_inst/FSM_onehot_state[2]_i_2_n_0 |                6 |             26 |
|  sys_clk_IBUF_BUFG | main_inst/cnt_0                            | main_inst/sys_rst_n                       |                9 |             27 |
|  sys_clk_IBUF_BUFG |                                            |                                           |               21 |             28 |
|  sys_clk_IBUF_BUFG |                                            | main_inst/sys_rst_n                       |               17 |             36 |
|  sys_clk_IBUF_BUFG |                                            | nixie_tube_inst/sys_rst_n                 |               37 |             70 |
+--------------------+--------------------------------------------+-------------------------------------------+------------------+----------------+


