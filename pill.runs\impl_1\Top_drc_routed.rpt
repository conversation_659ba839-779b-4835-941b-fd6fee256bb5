Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat May 31 15:19:45 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
| Design       : Top
| Device       : xc7a100tfgg484-1
| Speed File   : -1
| Design State : Fully Routed
---------------------------------------------------------------------------------------------------

Report DRC

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
           Ruledeck: default
             Max violations: <unlimited>
             Violations found: 5
+--------+----------+----------------------------+------------+
| Rule   | Severity | Description                | Violations |
+--------+----------+----------------------------+------------+
| DPIP-1 | Warning  | Input pipelining           | 2          |
| DPOP-1 | Warning  | PREG Output pipelining     | 1          |
| DPOP-2 | Warning  | MREG Output pipelining     | 1          |
| UCIO-1 | Warning  | Unconstrained Logical Port | 1          |
+--------+----------+----------------------------+------------+

2. REPORT DETAILS
-----------------
DPIP-1#1 Warning
Input pipelining  
DSP data_transform_inst/data0 input data_transform_inst/data0/A[29:0] is not pipelined. Pipelining DSP48 input will improve performance.
Related violations: <none>

DPIP-1#2 Warning
Input pipelining  
DSP data_transform_inst/data0 input data_transform_inst/data0/C[47:0] is not pipelined. Pipelining DSP48 input will improve performance.
Related violations: <none>

DPOP-1#1 Warning
PREG Output pipelining  
DSP data_transform_inst/data0 output data_transform_inst/data0/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
Related violations: <none>

DPOP-2#1 Warning
MREG Output pipelining  
DSP data_transform_inst/data0 multiplier stage data_transform_inst/data0/P[47:0] is not pipelined (MREG=0). Pipelining the multiplier function will improve performance and will save significant power so it is suggested whenever possible to fully pipeline this function.  If this multiplier was inferred, it is suggested to describe an additional register stage after this function.  If there is no registered adder/accumulator following the multiply function, two pipeline stages are suggested to allow both the MREG and PREG registers to be used.  If the DSP48 was instantiated in the design, it is suggested to set both the MREG and PREG attributes to 1 when performing multiply functions.
Related violations: <none>

UCIO-1#1 Warning
Unconstrained Logical Port  
16 out of 41 logical ports have no user assigned specific location constraint (LOC). This may cause I/O contention or incompatibility with the board power or connectivity affecting performance, signal integrity or in extreme cases cause damage to the device or the components to which it is connected. To correct this violation, specify all pin locations. This design will fail to generate a bitstream unless all logical ports have a user specified site LOC constraint defined.  To allow bitstream creation with unspecified pin locations (not recommended), use this command: set_property SEVERITY {Warning} [get_drc_checks UCIO-1].  NOTE: When using the Vivado Runs infrastructure (e.g. launch_runs Tcl command), add this command to a .tcl file and add that file as a pre-hook for write_bitstream step for the implementation run.  Problem ports: col[3:0], row[3:0], h_bw, h_gw, h_qw, h_sw, l_bw, l_gw, l_qw, l_sw.
Related violations: <none>


