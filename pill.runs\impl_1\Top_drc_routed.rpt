Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat May 31 14:46:05 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
| Design       : Top
| Device       : xc7a100tfgg484-1
| Speed File   : -1
| Design State : Fully Routed
---------------------------------------------------------------------------------------------------

Report DRC

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
           Ruledeck: default
             Max violations: <unlimited>
             Violations found: 5
+----------+----------+-----------------------------------------------------+------------+
| Rule     | Severity | Description                                         | Violations |
+----------+----------+-----------------------------------------------------+------------+
| CFGBVS-1 | Warning  | Missing CFGBVS and CONFIG_VOLTAGE Design Properties | 1          |
| DPIP-1   | Warning  | Input pipelining                                    | 2          |
| DPOP-1   | Warning  | PREG Output pipelining                              | 1          |
| DPOP-2   | Warning  | MREG Output pipelining                              | 1          |
+----------+----------+-----------------------------------------------------+------------+

2. REPORT DETAILS
-----------------
CFGBVS-1#1 Warning
Missing CFGBVS and CONFIG_VOLTAGE Design Properties  
Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
Related violations: <none>

DPIP-1#1 Warning
Input pipelining  
DSP data_transform_inst/data0 input data_transform_inst/data0/A[29:0] is not pipelined. Pipelining DSP48 input will improve performance.
Related violations: <none>

DPIP-1#2 Warning
Input pipelining  
DSP data_transform_inst/data0 input data_transform_inst/data0/C[47:0] is not pipelined. Pipelining DSP48 input will improve performance.
Related violations: <none>

DPOP-1#1 Warning
PREG Output pipelining  
DSP data_transform_inst/data0 output data_transform_inst/data0/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
Related violations: <none>

DPOP-2#1 Warning
MREG Output pipelining  
DSP data_transform_inst/data0 multiplier stage data_transform_inst/data0/P[47:0] is not pipelined (MREG=0). Pipelining the multiplier function will improve performance and will save significant power so it is suggested whenever possible to fully pipeline this function.  If this multiplier was inferred, it is suggested to describe an additional register stage after this function.  If there is no registered adder/accumulator following the multiply function, two pipeline stages are suggested to allow both the MREG and PREG registers to be used.  If the DSP48 was instantiated in the design, it is suggested to set both the MREG and PREG attributes to 1 when performing multiply functions.
Related violations: <none>


