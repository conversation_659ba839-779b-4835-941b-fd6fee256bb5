#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sat May 31 14:39:35 2025
# Process ID: 17904
# Current directory: E:/Final_pro/pill/pill.runs/synth_1
# Command line: vivado.exe -log Top.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source Top.tcl
# Log file: E:/Final_pro/pill/pill.runs/synth_1/Top.vds
# Journal file: E:/Final_pro/pill/pill.runs/synth_1\vivado.jou
#-----------------------------------------------------------
source Top.tcl -notrace
Command: synth_design -top Top -part xc7a100tfgg484-1
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 14116 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 430.984 ; gain = 98.551
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'Top' [E:/vsPro/BUPT_Digital_Logic-main/Top.v:23]
INFO: [Synth 8-6157] synthesizing module 'keyboard' [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:1]
	Parameter row_scan_period bound to: 25'b0000111101000010010000000 
	Parameter debounce_period bound to: 26'b00000111101000010010000000 
	Parameter ack_debounce_period bound to: 26'b00000111101000010010000000 
	Parameter first_row bound to: 4'b1110 
	Parameter second_row bound to: 4'b1101 
	Parameter third_row bound to: 4'b1011 
	Parameter fourth_row bound to: 4'b0111 
	Parameter first_col bound to: 4'b1110 
	Parameter second_col bound to: 4'b1101 
	Parameter third_col bound to: 4'b1011 
	Parameter fourth_col bound to: 4'b0111 
	Parameter IDLE bound to: 2'b00 
	Parameter PRESSED bound to: 2'b01 
	Parameter RELEASED bound to: 2'b10 
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:77]
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:133]
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:141]
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:149]
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:157]
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:131]
WARNING: [Synth 8-5788] Register ack_debounce_cnt_reg in module keyboard is has both Set and reset with same priority. This may cause simulation mismatches. Consider rewriting code  [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:120]
INFO: [Synth 8-6155] done synthesizing module 'keyboard' (1#1) [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:1]
INFO: [Synth 8-6157] synthesizing module 'main' [E:/vsPro/BUPT_Digital_Logic-main/main.v:22]
	Parameter ordinary bound to: 1'b0 
	Parameter customization bound to: 1'b1 
	Parameter setting bound to: 1'b0 
	Parameter working bound to: 1'b1 
	Parameter max_bot bound to: 14'b00001111100111 
	Parameter max_pil bound to: 14'b00001111100111 
	Parameter debounce_period bound to: 26'b00000111101000010010000000 
	Parameter IDLE bound to: 2'b00 
	Parameter PRESSED bound to: 2'b01 
	Parameter RELEASED bound to: 2'b11 
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/main.v:150]
WARNING: [Synth 8-5788] Register finish_bot_reg in module main is has both Set and reset with same priority. This may cause simulation mismatches. Consider rewriting code  [E:/vsPro/BUPT_Digital_Logic-main/main.v:106]
INFO: [Synth 8-6155] done synthesizing module 'main' (2#1) [E:/vsPro/BUPT_Digital_Logic-main/main.v:22]
INFO: [Synth 8-6157] synthesizing module 'nixie_tube' [E:/vsPro/BUPT_Digital_Logic-main/nixie_tube.v:6]
	Parameter show_zero bound to: 8'b11000000 
	Parameter show_one bound to: 8'b11111001 
	Parameter show_two bound to: 8'b10100100 
	Parameter show_three bound to: 8'b10110000 
	Parameter show_four bound to: 8'b10011001 
	Parameter show_five bound to: 8'b10010010 
	Parameter show_six bound to: 8'b10000010 
	Parameter show_seven bound to: 8'b11111000 
	Parameter show_eight bound to: 8'b10000000 
	Parameter show_nine bound to: 8'b10010000 
INFO: [Synth 8-226] default block is never used [E:/vsPro/BUPT_Digital_Logic-main/nixie_tube.v:101]
INFO: [Synth 8-6155] done synthesizing module 'nixie_tube' (3#1) [E:/vsPro/BUPT_Digital_Logic-main/nixie_tube.v:6]
INFO: [Synth 8-6157] synthesizing module 'data_transform' [E:/vsPro/BUPT_Digital_Logic-main/data_transform.v:23]
	Parameter work_dis bound to: 1'b1 
	Parameter set_dis bound to: 1'b0 
	Parameter setting bound to: 1'b0 
	Parameter working bound to: 1'b1 
INFO: [Synth 8-6155] done synthesizing module 'data_transform' (4#1) [E:/vsPro/BUPT_Digital_Logic-main/data_transform.v:23]
INFO: [Synth 8-6157] synthesizing module 'light_control' [E:/vsPro/BUPT_Digital_Logic-main/light_control.v:23]
	Parameter setting bound to: 1'b0 
	Parameter working bound to: 1'b1 
	Parameter green_0 bound to: 8'b00000000 
	Parameter green_1 bound to: 8'b00000001 
	Parameter green_2 bound to: 8'b00000010 
	Parameter green_3 bound to: 8'b00000100 
	Parameter green_4 bound to: 8'b00001000 
	Parameter green_5 bound to: 8'b00010000 
	Parameter green_6 bound to: 8'b00100000 
	Parameter green_7 bound to: 8'b01000000 
	Parameter green_8 bound to: 8'b10000000 
	Parameter green_all bound to: 8'b11111111 
INFO: [Synth 8-155] case statement is not full and has no default [E:/vsPro/BUPT_Digital_Logic-main/light_control.v:79]
INFO: [Synth 8-6155] done synthesizing module 'light_control' (5#1) [E:/vsPro/BUPT_Digital_Logic-main/light_control.v:23]
INFO: [Synth 8-6155] done synthesizing module 'Top' (6#1) [E:/vsPro/BUPT_Digital_Logic-main/Top.v:23]
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 486.293 ; gain = 153.859
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 486.293 ; gain = 153.859
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 486.293 ; gain = 153.859
---------------------------------------------------------------------------------
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:54]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:55]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:56]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:57]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:58]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:59]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:60]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:61]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:73]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:74]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:75]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:76]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:77]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:78]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:79]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:80]
Finished Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/Top_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/Top_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 843.715 ; gain = 0.000
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 843.770 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 843.770 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.002 . Memory (MB): peak = 843.770 ; gain = 0.000
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:06 ; elapsed = 00:00:07 . Memory (MB): peak = 843.770 ; gain = 511.336
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a100tfgg484-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:06 ; elapsed = 00:00:07 . Memory (MB): peak = 843.770 ; gain = 511.336
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:06 ; elapsed = 00:00:07 . Memory (MB): peak = 843.770 ; gain = 511.336
---------------------------------------------------------------------------------
INFO: [Synth 8-4471] merging register 'temp_data_reg[13:0]' into 'temp_reg[13:0]' [E:/vsPro/BUPT_Digital_Logic-main/keyboard.v:116]
INFO: [Synth 8-5546] ROM "row" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "debounce_cnt" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "key_pressed" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5544] ROM "state" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5546] ROM "char" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5544] ROM "char" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "char" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-802] inferred FSM for state register 'state_reg' in module 'keyboard'
INFO: [Synth 8-802] inferred FSM for state register 'finish_set_reg' in module 'main'
INFO: [Synth 8-802] inferred FSM for state register 'state_reg' in module 'main'
INFO: [Synth 8-5545] ROM "now_bot_bil_num" won't be mapped to RAM because address size (27) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "ack_debounce_cnt" won't be mapped to RAM because address size (26) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "state" won't be mapped to RAM because address size (26) is larger than maximum supported(25)
INFO: [Synth 8-5544] ROM "max_bot_num" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "max_sgl_bot" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "finish_set" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
INFO: [Synth 8-5545] ROM "now_bot_bil_num" won't be mapped to RAM because address size (27) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "ack_debounce_cnt" won't be mapped to RAM because address size (26) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "state" won't be mapped to RAM because address size (26) is larger than maximum supported(25)
INFO: [Synth 8-5544] ROM "max_bot_num" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "max_sgl_bot" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "finish_set" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
INFO: [Synth 8-5818] HDL ADVISOR - The operator resource <adder> is shared. To prevent sharing consider applying a KEEP on the output of the operator [E:/vsPro/BUPT_Digital_Logic-main/data_transform.v:94]
INFO: [Synth 8-5818] HDL ADVISOR - The operator resource <multiplier> is shared. To prevent sharing consider applying a KEEP on the output of the operator [E:/vsPro/BUPT_Digital_Logic-main/data_transform.v:94]
---------------------------------------------------------------------------------------------------
                   State |                     New Encoding |                Previous Encoding 
---------------------------------------------------------------------------------------------------
                    IDLE |                              001 |                               00
                 PRESSED |                              010 |                               01
                RELEASED |                              100 |                               10
---------------------------------------------------------------------------------------------------
INFO: [Synth 8-3354] encoded FSM with state register 'state_reg' using encoding 'one-hot' in module 'keyboard'
---------------------------------------------------------------------------------------------------
                   State |                     New Encoding |                Previous Encoding 
---------------------------------------------------------------------------------------------------
                    IDLE |                               00 |                               00
                 PRESSED |                               01 |                               01
                RELEASED |                               10 |                               11
---------------------------------------------------------------------------------------------------
INFO: [Synth 8-3354] encoded FSM with state register 'state_reg' using encoding 'sequential' in module 'main'
---------------------------------------------------------------------------------------------------
                   State |                     New Encoding |                Previous Encoding 
---------------------------------------------------------------------------------------------------
                 iSTATE1 |                              001 |                               00
                 iSTATE0 |                              010 |                               01
*
                  iSTATE |                              100 |                               10
---------------------------------------------------------------------------------------------------
INFO: [Synth 8-3354] encoded FSM with state register 'finish_set_reg' using encoding 'one-hot' in module 'main'
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:07 ; elapsed = 00:00:07 . Memory (MB): peak = 843.770 ; gain = 511.336
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     27 Bit       Adders := 1     
	   2 Input     26 Bit       Adders := 1     
	   2 Input     25 Bit       Adders := 3     
	   2 Input     14 Bit       Adders := 1     
	   2 Input      3 Bit       Adders := 1     
+---Registers : 
	               32 Bit    Registers := 1     
	               27 Bit    Registers := 1     
	               26 Bit    Registers := 1     
	               25 Bit    Registers := 3     
	               14 Bit    Registers := 4     
	                8 Bit    Registers := 2     
	                4 Bit    Registers := 12    
	                3 Bit    Registers := 1     
	                2 Bit    Registers := 2     
	                1 Bit    Registers := 16    
+---Muxes : 
	   2 Input     28 Bit        Muxes := 1     
	   2 Input     27 Bit        Muxes := 1     
	   2 Input     26 Bit        Muxes := 2     
	   3 Input     26 Bit        Muxes := 1     
	   2 Input     25 Bit        Muxes := 3     
	   3 Input     25 Bit        Muxes := 1     
	   2 Input     14 Bit        Muxes := 7     
	  13 Input      8 Bit        Muxes := 1     
	   5 Input      4 Bit        Muxes := 3     
	   2 Input      4 Bit        Muxes := 2     
	  13 Input      4 Bit        Muxes := 1     
	   3 Input      3 Bit        Muxes := 1     
	   2 Input      3 Bit        Muxes := 1     
	   4 Input      2 Bit        Muxes := 1     
	   3 Input      2 Bit        Muxes := 1     
	   2 Input      2 Bit        Muxes := 1     
	   7 Input      2 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 35    
	   5 Input      1 Bit        Muxes := 4     
	   6 Input      1 Bit        Muxes := 1     
	   3 Input      1 Bit        Muxes := 9     
	   4 Input      1 Bit        Muxes := 2     
	   8 Input      1 Bit        Muxes := 8     
	  11 Input      1 Bit        Muxes := 1     
	  10 Input      1 Bit        Muxes := 1     
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
Module keyboard 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     25 Bit       Adders := 3     
+---Registers : 
	               25 Bit    Registers := 3     
	               14 Bit    Registers := 1     
	                4 Bit    Registers := 2     
	                2 Bit    Registers := 1     
	                1 Bit    Registers := 3     
+---Muxes : 
	   2 Input     25 Bit        Muxes := 3     
	   3 Input     25 Bit        Muxes := 1     
	   2 Input     14 Bit        Muxes := 2     
	   5 Input      4 Bit        Muxes := 3     
	   2 Input      4 Bit        Muxes := 2     
	   4 Input      2 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 7     
	   5 Input      1 Bit        Muxes := 3     
	   6 Input      1 Bit        Muxes := 1     
	   3 Input      1 Bit        Muxes := 4     
Module main 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     27 Bit       Adders := 1     
	   2 Input     26 Bit       Adders := 1     
	   2 Input     14 Bit       Adders := 1     
+---Registers : 
	               27 Bit    Registers := 1     
	               26 Bit    Registers := 1     
	               14 Bit    Registers := 3     
	                1 Bit    Registers := 4     
+---Muxes : 
	   2 Input     27 Bit        Muxes := 1     
	   2 Input     26 Bit        Muxes := 2     
	   3 Input     26 Bit        Muxes := 1     
	   2 Input     14 Bit        Muxes := 3     
	   3 Input      3 Bit        Muxes := 1     
	   3 Input      2 Bit        Muxes := 1     
	   2 Input      2 Bit        Muxes := 1     
	   3 Input      1 Bit        Muxes := 5     
	   2 Input      1 Bit        Muxes := 19    
	   4 Input      1 Bit        Muxes := 2     
Module nixie_tube 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input      3 Bit       Adders := 1     
+---Registers : 
	                8 Bit    Registers := 1     
	                4 Bit    Registers := 9     
	                3 Bit    Registers := 1     
	                1 Bit    Registers := 8     
+---Muxes : 
	   2 Input      3 Bit        Muxes := 1     
	   8 Input      1 Bit        Muxes := 8     
	  11 Input      1 Bit        Muxes := 1     
Module data_transform 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 1     
+---Muxes : 
	   2 Input     28 Bit        Muxes := 1     
	   2 Input     14 Bit        Muxes := 2     
Module light_control 
Detailed RTL Component Info : 
+---Registers : 
	                8 Bit    Registers := 1     
	                4 Bit    Registers := 1     
	                2 Bit    Registers := 1     
	                1 Bit    Registers := 1     
+---Muxes : 
	  13 Input      8 Bit        Muxes := 1     
	  13 Input      4 Bit        Muxes := 1     
	   7 Input      2 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 9     
	  10 Input      1 Bit        Muxes := 1     
	   5 Input      1 Bit        Muxes := 1     
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 240 (col length:80)
BRAMs: 270 (col length: RAMB18 80 RAMB36 40)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
INFO: [Synth 8-5546] ROM "keyboard_inst/key_pressed" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "keyboard_inst/row" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "keyboard_inst/char" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5545] ROM "main_inst/ack_debounce_cnt" won't be mapped to RAM because address size (26) is larger than maximum supported(25)
DSP Report: Generating DSP data_transform_inst/data0, operation Mode is: C+(A:0x2710)*B.
DSP Report: operator data_transform_inst/data0 is absorbed into DSP data_transform_inst/data0.
DSP Report: operator p_0_out is absorbed into DSP data_transform_inst/data0.
INFO: [Synth 8-3886] merging instance 'data_transform_inst/data_reg[31]' (FDC) to 'data_transform_inst/data_reg[28]'
INFO: [Synth 8-3886] merging instance 'data_transform_inst/data_reg[29]' (FDC) to 'data_transform_inst/data_reg[28]'
INFO: [Synth 8-3886] merging instance 'data_transform_inst/data_reg[28]' (FDC) to 'data_transform_inst/data_reg[30]'
INFO: [Synth 8-3333] propagating constant 0 across sequential element (\data_transform_inst/data_reg[30] )
INFO: [Synth 8-3333] propagating constant 1 across sequential element (nixie_tube_inst/\smg_reg[7] )
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:21 ; elapsed = 00:00:22 . Memory (MB): peak = 972.734 ; gain = 640.301
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

DSP: Preliminary Mapping  Report (see note below)
+---------------+----------------+--------+--------+--------+--------+--------+------+------+------+------+-------+------+------+
|Module Name    | DSP Mapping    | A Size | B Size | C Size | D Size | P Size | AREG | BREG | CREG | DREG | ADREG | MREG | PREG | 
+---------------+----------------+--------+--------+--------+--------+--------+------+------+------+------+-------+------+------+
|data_transform | C+(A:0x2710)*B | 14     | 14     | 14     | -      | 28     | 0    | 0    | 0    | -    | -     | 0    | 0    | 
+---------------+----------------+--------+--------+--------+--------+--------+------+------+------+------+-------+------+------+

Note: The table above is a preliminary report that shows the DSPs inferred at the current stage of the synthesis flow. Some DSP may be reimplemented as non DSP primitives later in the synthesis flow. Multiple instantiated DSPs are reported only once.
---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:27 ; elapsed = 00:00:28 . Memory (MB): peak = 972.734 ; gain = 640.301
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:42 ; elapsed = 00:00:43 . Memory (MB): peak = 1164.809 ; gain = 832.375
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:43 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:44 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:44 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:44 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:44 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:44 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:44 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+-+--------------+----------+
| |BlackBox name |Instances |
+-+--------------+----------+
+-+--------------+----------+

Report Cell Usage: 
+------+--------+------+
|      |Cell    |Count |
+------+--------+------+
|1     |BUFG    |     1|
|2     |CARRY4  |   641|
|3     |DSP48E1 |     1|
|4     |LUT1    |    87|
|5     |LUT2    |   797|
|6     |LUT3    |   883|
|7     |LUT4    |   736|
|8     |LUT5    |   308|
|9     |LUT6    |  1241|
|10    |MUXF7   |     4|
|11    |FDCE    |   315|
|12    |FDPE    |    15|
|13    |FDRE    |    54|
|14    |IBUF    |    10|
|15    |OBUF    |    31|
+------+--------+------+

Report Instance Areas: 
+------+----------------------+---------------+------+
|      |Instance              |Module         |Cells |
+------+----------------------+---------------+------+
|1     |top                   |               |  5124|
|2     |  data_transform_inst |data_transform |  2354|
|3     |  keyboard_inst       |keyboard       |   253|
|4     |  light_control_inst  |light_control  |    87|
|5     |  main_inst           |main           |   367|
|6     |  nixie_tube_inst     |nixie_tube     |   498|
+------+----------------------+---------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:44 ; elapsed = 00:00:45 . Memory (MB): peak = 1173.918 ; gain = 841.484
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 0 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:39 ; elapsed = 00:00:42 . Memory (MB): peak = 1173.918 ; gain = 484.008
Synthesis Optimization Complete : Time (s): cpu = 00:00:44 ; elapsed = 00:00:46 . Memory (MB): peak = 1173.918 ; gain = 841.484
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 646 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1173.918 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

INFO: [Common 17-83] Releasing license: Synthesis
70 Infos, 2 Warnings, 16 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:46 ; elapsed = 00:00:48 . Memory (MB): peak = 1173.918 ; gain = 854.430
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1173.918 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/synth_1/Top.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file Top_utilization_synth.rpt -pb Top_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Sat May 31 14:40:25 2025...
