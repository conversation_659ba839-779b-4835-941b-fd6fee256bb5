# TCL脚本：检查约束文件中的端口约束
# 用于验证所有端口都有正确的约束

puts "=== 检查端口约束 ==="

# 获取所有端口
set all_ports [get_ports]
puts "总端口数: [llength $all_ports]"

# 检查每个端口的约束
puts "\n=== 端口约束详情 ==="
foreach port $all_ports {
    set port_name [get_property NAME $port]
    
    # 检查是否有PACKAGE_PIN约束
    set has_loc 0
    if {[catch {get_property PACKAGE_PIN $port} loc_pin]} {
        set loc_pin "未约束"
    } else {
        set has_loc 1
    }
    
    # 检查是否有IOSTANDARD约束
    if {[catch {get_property IOSTANDARD $port} iostd]} {
        set iostd "未设置"
    }
    
    if {$has_loc == 0} {
        puts "❌ $port_name: LOC=$loc_pin, IOSTANDARD=$iostd"
    } else {
        puts "✅ $port_name: LOC=$loc_pin, IOSTANDARD=$iostd"
    }
}

# 检查特定的问题端口
puts "\n=== 检查问题端口 ==="
set problem_ports {smg[0] smg[1] smg[2] smg[3] smg[4] smg[5] smg[6] smg[7] h_bw h_gw h_qw h_sw l_bw l_gw l_qw l_sw}

foreach port_pattern $problem_ports {
    if {[catch {get_ports $port_pattern} ports]} {
        puts "❌ 端口 $port_pattern 不存在"
    } else {
        foreach port $ports {
            set port_name [get_property NAME $port]
            if {[catch {get_property PACKAGE_PIN $port} loc_pin]} {
                puts "❌ $port_name: 缺少PACKAGE_PIN约束"
            } else {
                puts "✅ $port_name: PACKAGE_PIN=$loc_pin"
            }
        }
    }
}

puts "\n=== 检查完成 ==="
