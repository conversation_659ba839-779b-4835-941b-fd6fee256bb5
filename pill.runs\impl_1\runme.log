
*** Running vivado
    with args -log Top.vdi -applog -m64 -product Vivado -messageDb vivado.pb -mode batch -source Top.tcl -notrace


****** Vivado v2018.3 (64-bit)
  **** SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
  **** IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

source Top.tcl -notrace
Command: link_design -top Top -part xc7a100tfgg484-1
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Netlist 29-17] Analyzing 646 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:54]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:55]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:56]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:57]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:58]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:59]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:60]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:61]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:73]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:74]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:75]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:76]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:77]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:78]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:79]
CRITICAL WARNING: [Common 17-161] Invalid option value '#' specified for 'objects'. [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc:80]
Finished Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 669.367 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

7 Infos, 0 Warnings, 16 Critical Warnings and 0 Errors encountered.
link_design completed successfully
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.353 . Memory (MB): peak = 678.293 ; gain = 8.926

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: efd75b78

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1197.691 ; gain = 519.398

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: efd75b78

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.123 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Retarget created 0 cells and removed 0 cells

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 2 Constant propagation | Checksum: 1b76d97b6

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.163 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Constant propagation created 0 cells and removed 0 cells

Phase 3 Sweep
Phase 3 Sweep | Checksum: 1c1d1e3d3

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.233 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 0 cells

Phase 4 BUFG optimization
Phase 4 BUFG optimization | Checksum: 1c1d1e3d3

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.333 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: 1facb0465

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.535 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: 1facb0465

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.549 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |               0  |               0  |                                              0  |
|  Constant propagation         |               0  |               0  |                                              0  |
|  Sweep                        |               0  |               0  |                                              0  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                              0  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 1294.266 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 1facb0465

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.560 . Memory (MB): peak = 1294.266 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
Ending Power Optimization Task | Checksum: 1facb0465

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1294.266 ; gain = 0.000

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: 1facb0465

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1294.266 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1294.266 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 1facb0465

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
23 Infos, 0 Warnings, 16 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:07 ; elapsed = 00:00:07 . Memory (MB): peak = 1294.266 ; gain = 624.898
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.020 . Memory (MB): peak = 1294.266 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1294.266 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file Top_drc_opted.rpt -pb Top_drc_opted.pb -rpx Top_drc_opted.rpx
Command: report_drc -file Top_drc_opted.rpt -pb Top_drc_opted.pb -rpx Top_drc_opted.rpx
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'D:/Xilinx/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/Final_pro/pill/pill.runs/impl_1/Top_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1294.266 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: 110964194

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1294.266 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1294.266 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 1b3bd563d

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.614 . Memory (MB): peak = 1319.805 ; gain = 25.539

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 1f299bdca

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 1f299bdca

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1345.461 ; gain = 51.195
Phase 1 Placer Initialization | Checksum: 1f299bdca

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 21feac8f9

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 2.2 Physical Synthesis In Placer
INFO: [Physopt 32-65] No nets found for high-fanout optimization.
INFO: [Physopt 32-232] Optimized 0 net. Created 0 new instance.
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-456] No candidate cells for DSP register optimization found in the design.
INFO: [Physopt 32-775] End 2 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-677] No candidate cells for Shift Register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-526] No candidate cells for BRAM register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-949] No candidate nets found for HD net replication
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1345.461 ; gain = 0.000

Summary of Physical Synthesis Optimizations
============================================


----------------------------------------------------------------------------------------------------------------------------------------
|  Optimization                  |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
----------------------------------------------------------------------------------------------------------------------------------------
|  Very High Fanout              |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  DSP Register                  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Shift Register                |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  BRAM Register                 |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  HD Interface Net Replication  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Total                         |            0  |              0  |                     0  |           0  |           5  |  00:00:00  |
----------------------------------------------------------------------------------------------------------------------------------------


Phase 2.2 Physical Synthesis In Placer | Checksum: 1290b926e

Time (s): cpu = 00:00:07 ; elapsed = 00:00:05 . Memory (MB): peak = 1345.461 ; gain = 51.195
Phase 2 Global Placement | Checksum: 1906589c9

Time (s): cpu = 00:00:07 ; elapsed = 00:00:05 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 1906589c9

Time (s): cpu = 00:00:07 ; elapsed = 00:00:05 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 189268da0

Time (s): cpu = 00:00:08 ; elapsed = 00:00:06 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 1ecfbf329

Time (s): cpu = 00:00:08 ; elapsed = 00:00:06 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 211835d6c

Time (s): cpu = 00:00:08 ; elapsed = 00:00:06 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.5 Fast Optimization
Phase 3.5 Fast Optimization | Checksum: 26bdd67f7

Time (s): cpu = 00:00:09 ; elapsed = 00:00:07 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.6 Small Shape Detail Placement
Phase 3.6 Small Shape Detail Placement | Checksum: 1c408c83f

Time (s): cpu = 00:00:10 ; elapsed = 00:00:08 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.7 Re-assign LUT pins
Phase 3.7 Re-assign LUT pins | Checksum: 22d9fa437

Time (s): cpu = 00:00:10 ; elapsed = 00:00:08 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.8 Pipeline Register Optimization
Phase 3.8 Pipeline Register Optimization | Checksum: 24823727e

Time (s): cpu = 00:00:10 ; elapsed = 00:00:08 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 3.9 Fast Optimization
Phase 3.9 Fast Optimization | Checksum: 2359f0408

Time (s): cpu = 00:00:13 ; elapsed = 00:00:12 . Memory (MB): peak = 1345.461 ; gain = 51.195
Phase 3 Detail Placement | Checksum: 2359f0408

Time (s): cpu = 00:00:13 ; elapsed = 00:00:12 . Memory (MB): peak = 1345.461 ; gain = 51.195

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
Post Placement Optimization Initialization | Checksum: 27ba75ab9

Phase ******* BUFG Insertion
INFO: [Place 46-46] BUFG insertion identified 0 candidate nets, 0 success, 0 bufg driver replicated, 0 skipped for placement/routing, 0 skipped for timing, 0 skipped for netlist change reason
Phase ******* BUFG Insertion | Checksum: 27ba75ab9

Time (s): cpu = 00:00:13 ; elapsed = 00:00:12 . Memory (MB): peak = 1352.480 ; gain = 58.215
INFO: [Place 30-746] Post Placement Timing Summary WNS=-13.482. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: 223ab0c61

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1352.863 ; gain = 58.598
Phase 4.1 Post Commit Optimization | Checksum: 223ab0c61

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1352.863 ; gain = 58.598

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 223ab0c61

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1352.863 ; gain = 58.598

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 223ab0c61

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1352.863 ; gain = 58.598

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1352.863 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 1ce0fb3fa

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1352.863 ; gain = 58.598
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 1ce0fb3fa

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1352.863 ; gain = 58.598
Ending Placer Task | Checksum: 10703b627

Time (s): cpu = 00:00:20 ; elapsed = 00:00:19 . Memory (MB): peak = 1352.863 ; gain = 58.598
INFO: [Common 17-83] Releasing license: Implementation
55 Infos, 0 Warnings, 16 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:00:21 ; elapsed = 00:00:20 . Memory (MB): peak = 1352.863 ; gain = 58.598
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1352.863 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1360.465 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.254 . Memory (MB): peak = 1360.484 ; gain = 7.621
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file Top_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.039 . Memory (MB): peak = 1360.484 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file Top_utilization_placed.rpt -pb Top_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file Top_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 1360.484 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: 6dd5e69c ConstDB: 0 ShapeSum: 992dcf8b RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 9ec834b5

Time (s): cpu = 00:00:18 ; elapsed = 00:00:15 . Memory (MB): peak = 1512.992 ; gain = 151.574
Post Restoration Checksum: NetGraph: 3505a6f6 NumContArr: 69c28dbf Constraints: 0 Timing: 0

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: 9ec834b5

Time (s): cpu = 00:00:18 ; elapsed = 00:00:15 . Memory (MB): peak = 1531.156 ; gain = 169.738

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: 9ec834b5

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1537.434 ; gain = 176.016

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: 9ec834b5

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1537.434 ; gain = 176.016
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: 19978fa89

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1555.797 ; gain = 194.379
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-13.120| TNS=-351.450| WHS=-0.098 | THS=-2.565 |

Phase 2 Router Initialization | Checksum: 120c51f25

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1559.652 ; gain = 198.234

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: 13f7c46c9

Time (s): cpu = 00:00:20 ; elapsed = 00:00:17 . Memory (MB): peak = 1564.555 ; gain = 203.137

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 2740
 Number of Nodes with overlaps = 1098
 Number of Nodes with overlaps = 445
 Number of Nodes with overlaps = 204
 Number of Nodes with overlaps = 75
 Number of Nodes with overlaps = 41
 Number of Nodes with overlaps = 19
 Number of Nodes with overlaps = 8
 Number of Nodes with overlaps = 4
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-15.816| TNS=-432.182| WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 1e4ed1366

Time (s): cpu = 00:00:38 ; elapsed = 00:00:28 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 4.2 Global Iteration 1
 Number of Nodes with overlaps = 619
 Number of Nodes with overlaps = 384
 Number of Nodes with overlaps = 199
 Number of Nodes with overlaps = 92
 Number of Nodes with overlaps = 70
 Number of Nodes with overlaps = 39
 Number of Nodes with overlaps = 15
 Number of Nodes with overlaps = 7
 Number of Nodes with overlaps = 7
 Number of Nodes with overlaps = 2
 Number of Nodes with overlaps = 2
 Number of Nodes with overlaps = 2
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 6
 Number of Nodes with overlaps = 2
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-15.361| TNS=-430.563| WHS=N/A    | THS=N/A    |

Phase 4.2 Global Iteration 1 | Checksum: f404836a

Time (s): cpu = 00:00:53 ; elapsed = 00:00:43 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 4.3 Global Iteration 2
 Number of Nodes with overlaps = 874
 Number of Nodes with overlaps = 395
 Number of Nodes with overlaps = 213
 Number of Nodes with overlaps = 49
 Number of Nodes with overlaps = 30
 Number of Nodes with overlaps = 12
 Number of Nodes with overlaps = 6
 Number of Nodes with overlaps = 6
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-15.161| TNS=-427.610| WHS=N/A    | THS=N/A    |

Phase 4.3 Global Iteration 2 | Checksum: 135765471

Time (s): cpu = 00:01:07 ; elapsed = 00:00:54 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 4.4 Global Iteration 3
 Number of Nodes with overlaps = 688
 Number of Nodes with overlaps = 200
 Number of Nodes with overlaps = 129
 Number of Nodes with overlaps = 55
 Number of Nodes with overlaps = 61
 Number of Nodes with overlaps = 39
 Number of Nodes with overlaps = 38
 Number of Nodes with overlaps = 21
 Number of Nodes with overlaps = 13
 Number of Nodes with overlaps = 10
 Number of Nodes with overlaps = 4
 Number of Nodes with overlaps = 8
 Number of Nodes with overlaps = 6
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-14.786| TNS=-425.006| WHS=N/A    | THS=N/A    |

Phase 4.4 Global Iteration 3 | Checksum: 1e0634f87

Time (s): cpu = 00:01:15 ; elapsed = 00:00:59 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 4.5 Global Iteration 4
 Number of Nodes with overlaps = 721
 Number of Nodes with overlaps = 335
 Number of Nodes with overlaps = 163
 Number of Nodes with overlaps = 73
 Number of Nodes with overlaps = 41
 Number of Nodes with overlaps = 10
 Number of Nodes with overlaps = 11
 Number of Nodes with overlaps = 3
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-15.341| TNS=-426.438| WHS=N/A    | THS=N/A    |

Phase 4.5 Global Iteration 4 | Checksum: 187d0d4e8

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055
Phase 4 Rip-up And Reroute | Checksum: 187d0d4e8

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp

Phase 5.1.1 Update Timing
Phase 5.1.1 Update Timing | Checksum: 1c519cf3c

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-14.706| TNS=-422.118| WHS=N/A    | THS=N/A    |

 Number of Nodes with overlaps = 0
Phase 5.1 Delay CleanUp | Checksum: 1214979c3

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: 1214979c3

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055
Phase 5 Delay and Skew Optimization | Checksum: 1214979c3

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 159378f03

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-14.569| TNS=-413.649| WHS=0.159  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: 159378f03

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055
Phase 6 Post Hold Fix | Checksum: 159378f03

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 1.10315 %
  Global Horizontal Routing Utilization  = 1.47208 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Congestion Report
North Dir 1x1 Area, Max Cong = 57.6577%, No Congested Regions.
South Dir 1x1 Area, Max Cong = 69.3694%, No Congested Regions.
East Dir 1x1 Area, Max Cong = 63.2353%, No Congested Regions.
West Dir 1x1 Area, Max Cong = 75%, No Congested Regions.

------------------------------
Reporting congestion hotspots
------------------------------
Direction: North
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: South
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: East
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: West
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0

Phase 7 Route finalize | Checksum: e6a09678

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: e6a09678

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: a1e072f5

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=-14.569| TNS=-413.649| WHS=0.159  | THS=0.000  |

WARNING: [Route 35-328] Router estimated timing not met.
Resolution: For a complete and accurate timing signoff, report_timing_summary must be run after route_design. Alternatively, route_design can be run with the -timing_summary option to enable a complete timing signoff at the end of route_design.
Phase 10 Post Router Timing | Checksum: a1e072f5

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:01:23 ; elapsed = 00:01:05 . Memory (MB): peak = 1565.473 ; gain = 204.055

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
76 Infos, 1 Warnings, 16 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:01:24 ; elapsed = 00:01:06 . Memory (MB): peak = 1565.473 ; gain = 204.988
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1565.473 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1565.473 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.288 . Memory (MB): peak = 1565.473 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
Command: report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/Final_pro/pill/pill.runs/impl_1/Top_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file Top_methodology_drc_routed.rpt -pb Top_methodology_drc_routed.pb -rpx Top_methodology_drc_routed.rpx
Command: report_methodology -file Top_methodology_drc_routed.rpt -pb Top_methodology_drc_routed.pb -rpx Top_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file E:/Final_pro/pill/pill.runs/impl_1/Top_methodology_drc_routed.rpt.
report_methodology completed successfully
INFO: [runtcl-4] Executing : report_power -file Top_power_routed.rpt -pb Top_power_summary_routed.pb -rpx Top_power_routed.rpx
Command: report_power -file Top_power_routed.rpt -pb Top_power_summary_routed.pb -rpx Top_power_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
88 Infos, 1 Warnings, 16 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file Top_route_status.rpt -pb Top_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file Top_timing_summary_routed.rpt -pb Top_timing_summary_routed.pb -rpx Top_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
CRITICAL WARNING: [Timing 38-282] The design failed to meet the timing requirements. Please see the timing summary report for details on the timing violations.
INFO: [runtcl-4] Executing : report_incremental_reuse -file Top_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file Top_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file Top_bus_skew_routed.rpt -pb Top_bus_skew_routed.pb -rpx Top_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
Command: write_bitstream -force Top.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC CFGBVS-1] Missing CFGBVS and CONFIG_VOLTAGE Design Properties: Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
WARNING: [DRC DPIP-1] Input pipelining: DSP data_transform_inst/data0 input data_transform_inst/data0/A[29:0] is not pipelined. Pipelining DSP48 input will improve performance.
WARNING: [DRC DPIP-1] Input pipelining: DSP data_transform_inst/data0 input data_transform_inst/data0/C[47:0] is not pipelined. Pipelining DSP48 input will improve performance.
WARNING: [DRC DPOP-1] PREG Output pipelining: DSP data_transform_inst/data0 output data_transform_inst/data0/P[47:0] is not pipelined (PREG=0). Pipelining the DSP48 output will improve performance and often saves power so it is suggested whenever possible to fully pipeline this function.  If this DSP48 function was inferred, it is suggested to describe an additional register stage after this function.  If the DSP48 was instantiated in the design, it is suggested to set the PREG attribute to 1.
WARNING: [DRC DPOP-2] MREG Output pipelining: DSP data_transform_inst/data0 multiplier stage data_transform_inst/data0/P[47:0] is not pipelined (MREG=0). Pipelining the multiplier function will improve performance and will save significant power so it is suggested whenever possible to fully pipeline this function.  If this multiplier was inferred, it is suggested to describe an additional register stage after this function.  If there is no registered adder/accumulator following the multiply function, two pipeline stages are suggested to allow both the MREG and PREG registers to be used.  If the DSP48 was instantiated in the design, it is suggested to set both the MREG and PREG attributes to 1 when performing multiply functions.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 5 Warnings
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Bitstream compression saved 22398848 bits.
Writing bitstream ./Top.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-120] WebTalk data collection is mandatory when using a WebPACK part without a full Vivado license. To see the specific WebTalk data collected for your design, open the usage_statistics_webtalk.html or usage_statistics_webtalk.xml file in the implementation directory.
INFO: [Common 17-83] Releasing license: Implementation
107 Infos, 6 Warnings, 17 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:10 ; elapsed = 00:00:09 . Memory (MB): peak = 2028.574 ; gain = 445.758
INFO: [Common 17-206] Exiting Vivado at Sat May 31 14:46:16 2025...
