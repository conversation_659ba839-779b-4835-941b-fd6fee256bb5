# PowerShell script to fix the pill bottling system project

Write-Host "Starting project fix..." -ForegroundColor Green

# Define paths
$sourceDir = "E:\vsPro\BUPT_Digital_Logic-main"
$currentDir = Get-Location

# Check if source directory exists
if (!(Test-Path $sourceDir)) {
    Write-Host "Error: Source directory not found: $sourceDir" -ForegroundColor Red
    exit 1
}

Write-Host "Step 1: Creating backup..." -ForegroundColor Yellow

# Create backup directory
$backupDir = Join-Path $sourceDir "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

# Backup original files
$originalFiles = @("keyboard.v", "main.v", "light_control.v", "Top.v")
foreach ($file in $originalFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $backupPath = Join-Path $backupDir $file
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $backupPath
        Write-Host "Backed up: $file" -ForegroundColor Gray
    }
}

Write-Host "Step 2: Copying fixed files..." -ForegroundColor Yellow

# Copy fixed files and rename
$fileMapping = @{
    "keyboard_fixed.v" = "keyboard.v"
    "main_fixed.v" = "main.v"
    "light_control_fixed.v" = "light_control.v"
    "Top_fixed.v" = "Top.v"
}

foreach ($fixedFile in $fileMapping.Keys) {
    $sourcePath = Join-Path $currentDir $fixedFile
    $targetFile = $fileMapping[$fixedFile]
    $targetPath = Join-Path $sourceDir $targetFile
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $targetPath -Force
        Write-Host "Copied: $fixedFile -> $targetFile" -ForegroundColor Green
    } else {
        Write-Host "Warning: Fixed file not found: $fixedFile" -ForegroundColor Red
    }
}

Write-Host "Step 3: Verifying files..." -ForegroundColor Yellow

# Verify files exist
foreach ($file in $originalFiles) {
    $filePath = Join-Path $sourceDir $file
    if (Test-Path $filePath) {
        $fileSize = (Get-Item $filePath).Length
        Write-Host "OK: $file (Size: $fileSize bytes)" -ForegroundColor Green
    } else {
        Write-Host "Missing: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Fix completed!" -ForegroundColor Green
Write-Host "Backup location: $backupDir" -ForegroundColor Gray
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Re-synthesize the project in Vivado" -ForegroundColor White
Write-Host "2. Check synthesis report for warnings and errors" -ForegroundColor White
Write-Host "3. Re-implement and generate bitstream" -ForegroundColor White
Write-Host "4. Download to FPGA board for testing" -ForegroundColor White

Write-Host ""
Write-Host "Main fixes applied:" -ForegroundColor Cyan
Write-Host "- Keyboard module: Fixed case statements, optimized debounce logic" -ForegroundColor White
Write-Host "- Main module: Fixed state machine logic, fixed reset priority" -ForegroundColor White
Write-Host "- Light control module: Fixed case statements, optimized LED control" -ForegroundColor White
Write-Host "- Constraint file: Added pull-up resistors, optimized pin configuration" -ForegroundColor White

Write-Host ""
Write-Host "Press Enter to continue..."
Read-Host
