#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sat May 31 14:17:36 2025
# Process ID: 25168
# Current directory: E:/Final_pro/pill/pill.runs/impl_1
# Command line: vivado.exe -log Top.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source Top.tcl -notrace
# Log file: E:/Final_pro/pill/pill.runs/impl_1/Top.vdi
# Journal file: E:/Final_pro/pill/pill.runs/impl_1\vivado.jou
#-----------------------------------------------------------
source Top.tcl -notrace
Command: link_design -top Top -part xc7a100tfgg484-1
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Netlist 29-17] Analyzing 646 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
Finished Parsing XDC File [E:/Final_pro/pill/pill.srcs/constrs_1/new/constraint.xdc]
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 658.410 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

7 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
link_design completed successfully
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.324 . Memory (MB): peak = 669.402 ; gain = 10.992

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: f85689e6

Time (s): cpu = 00:00:05 ; elapsed = 00:00:05 . Memory (MB): peak = 1189.801 ; gain = 520.398

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: f85689e6

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.105 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Retarget created 0 cells and removed 0 cells

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 2 Constant propagation | Checksum: 10688bda9

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.149 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Constant propagation created 0 cells and removed 0 cells

Phase 3 Sweep
Phase 3 Sweep | Checksum: 142e220d8

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.221 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 0 cells

Phase 4 BUFG optimization
Phase 4 BUFG optimization | Checksum: 142e220d8

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.307 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: 14abef860

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.513 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: 14abef860

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.527 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |               0  |               0  |                                              0  |
|  Constant propagation         |               0  |               0  |                                              0  |
|  Sweep                        |               0  |               0  |                                              0  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                              0  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 1287.266 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 14abef860

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.539 . Memory (MB): peak = 1287.266 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
Ending Power Optimization Task | Checksum: 14abef860

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1287.266 ; gain = 0.000

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: 14abef860

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1287.266 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1287.266 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 14abef860

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
23 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:06 ; elapsed = 00:00:07 . Memory (MB): peak = 1287.266 ; gain = 628.855
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.021 . Memory (MB): peak = 1287.266 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1287.266 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file Top_drc_opted.rpt -pb Top_drc_opted.pb -rpx Top_drc_opted.rpx
Command: report_drc -file Top_drc_opted.rpt -pb Top_drc_opted.pb -rpx Top_drc_opted.rpx
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'D:/Xilinx/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/Final_pro/pill/pill.runs/impl_1/Top_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1287.266 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: c26cb0fc

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.003 . Memory (MB): peak = 1287.266 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1287.266 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 17848bf1a

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.604 . Memory (MB): peak = 1308.637 ; gain = 21.371

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 1f8acdc73

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.773 . Memory (MB): peak = 1310.152 ; gain = 22.887

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 1f8acdc73

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.778 . Memory (MB): peak = 1310.152 ; gain = 22.887
Phase 1 Placer Initialization | Checksum: 1f8acdc73

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.780 . Memory (MB): peak = 1310.152 ; gain = 22.887

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 1f8acdc73

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.785 . Memory (MB): peak = 1312.055 ; gain = 24.789
WARNING: [Place 46-29] place_design is not in timing mode. Skip physical synthesis in placer
Phase 2 Global Placement | Checksum: 1f5b3d34e

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1322.145 ; gain = 34.879

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 1f5b3d34e

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1322.145 ; gain = 34.879

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: d881f58b

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1323.027 ; gain = 35.762

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 8583ee65

Time (s): cpu = 00:00:03 ; elapsed = 00:00:03 . Memory (MB): peak = 1323.070 ; gain = 35.805

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 8583ee65

Time (s): cpu = 00:00:03 ; elapsed = 00:00:03 . Memory (MB): peak = 1323.070 ; gain = 35.805

Phase 3.5 Small Shape Detail Placement
Phase 3.5 Small Shape Detail Placement | Checksum: 1820d2341

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188

Phase 3.6 Re-assign LUT pins
Phase 3.6 Re-assign LUT pins | Checksum: 1820d2341

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188

Phase 3.7 Pipeline Register Optimization
Phase 3.7 Pipeline Register Optimization | Checksum: 1820d2341

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188
Phase 3 Detail Placement | Checksum: 1820d2341

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
Phase 4.1 Post Commit Optimization | Checksum: 1820d2341

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 1820d2341

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 1820d2341

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1329.453 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 11051a682

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 11051a682

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188
Ending Placer Task | Checksum: cec41c57

Time (s): cpu = 00:00:04 ; elapsed = 00:00:03 . Memory (MB): peak = 1329.453 ; gain = 42.188
INFO: [Common 17-83] Releasing license: Implementation
41 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
place_design completed successfully
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1329.453 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1335.215 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.233 . Memory (MB): peak = 1335.215 ; gain = 5.762
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file Top_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.039 . Memory (MB): peak = 1335.215 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file Top_utilization_placed.rpt -pb Top_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file Top_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1335.215 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: c32f9f3f ConstDB: 0 ShapeSum: b947d18 RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 1038485c9

Time (s): cpu = 00:00:17 ; elapsed = 00:00:15 . Memory (MB): peak = 1495.141 ; gain = 157.977
Post Restoration Checksum: NetGraph: c7c6b8ee NumContArr: 3bbdccdb Constraints: 0 Timing: 0

Phase 2 Router Initialization
INFO: [Route 35-64] No timing constraints were detected. The router will operate in resource-optimization mode.

Phase 2.1 Fix Topology Constraints
Phase 2.1 Fix Topology Constraints | Checksum: 1038485c9

Time (s): cpu = 00:00:17 ; elapsed = 00:00:15 . Memory (MB): peak = 1501.184 ; gain = 164.020

Phase 2.2 Pre Route Cleanup
Phase 2.2 Pre Route Cleanup | Checksum: 1038485c9

Time (s): cpu = 00:00:17 ; elapsed = 00:00:15 . Memory (MB): peak = 1501.184 ; gain = 164.020
 Number of Nodes with overlaps = 0
Phase 2 Router Initialization | Checksum: de831daa

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: 18baa6172

Time (s): cpu = 00:00:18 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 578
 Number of Nodes with overlaps = 0
Phase 4.1 Global Iteration 0 | Checksum: *********

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391
Phase 4 Rip-up And Reroute | Checksum: *********

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391

Phase 5 Delay and Skew Optimization
Phase 5 Delay and Skew Optimization | Checksum: *********

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter
Phase 6.1 Hold Fix Iter | Checksum: *********

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391
Phase 6 Post Hold Fix | Checksum: *********

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 0.613527 %
  Global Horizontal Routing Utilization  = 0.768684 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Congestion Report
North Dir 1x1 Area, Max Cong = 34.2342%, No Congested Regions.
South Dir 1x1 Area, Max Cong = 39.6396%, No Congested Regions.
East Dir 1x1 Area, Max Cong = 41.1765%, No Congested Regions.
West Dir 1x1 Area, Max Cong = 38.2353%, No Congested Regions.

------------------------------
Reporting congestion hotspots
------------------------------
Direction: North
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: South
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: East
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: West
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0

Phase 7 Route finalize | Checksum: *********

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: *********

Time (s): cpu = 00:00:19 ; elapsed = 00:00:16 . Memory (MB): peak = 1525.555 ; gain = 188.391

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: d73f90fe

Time (s): cpu = 00:00:19 ; elapsed = 00:00:17 . Memory (MB): peak = 1525.555 ; gain = 188.391
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:00:19 ; elapsed = 00:00:17 . Memory (MB): peak = 1525.555 ; gain = 188.391

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
54 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:00:20 ; elapsed = 00:00:17 . Memory (MB): peak = 1525.555 ; gain = 190.340
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1525.555 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1525.555 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.278 . Memory (MB): peak = 1525.555 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/Final_pro/pill/pill.runs/impl_1/Top_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
Command: report_drc -file Top_drc_routed.rpt -pb Top_drc_routed.pb -rpx Top_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/Final_pro/pill/pill.runs/impl_1/Top_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file Top_methodology_drc_routed.rpt -pb Top_methodology_drc_routed.pb -rpx Top_methodology_drc_routed.rpx
Command: report_methodology -file Top_methodology_drc_routed.rpt -pb Top_methodology_drc_routed.pb -rpx Top_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file E:/Final_pro/pill/pill.runs/impl_1/Top_methodology_drc_routed.rpt.
report_methodology completed successfully
INFO: [runtcl-4] Executing : report_power -file Top_power_routed.rpt -pb Top_power_summary_routed.pb -rpx Top_power_routed.rpx
Command: report_power -file Top_power_routed.rpt -pb Top_power_summary_routed.pb -rpx Top_power_routed.rpx
WARNING: [Power 33-232] No user defined clocks were found in the design!
Resolution: Please specify clocks using create_clock/create_generated_clock for sequential elements. For pure combinatorial circuits, please specify a virtual clock, otherwise the vectorless estimation might be inaccurate
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
66 Infos, 2 Warnings, 0 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file Top_route_status.rpt -pb Top_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file Top_timing_summary_routed.rpt -pb Top_timing_summary_routed.pb -rpx Top_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
WARNING: [Timing 38-313] There are no user specified timing constraints. Timing constraints are needed for proper timing analysis.
INFO: [runtcl-4] Executing : report_incremental_reuse -file Top_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file Top_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file Top_bus_skew_routed.rpt -pb Top_bus_skew_routed.pb -rpx Top_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
INFO: [Common 17-206] Exiting Vivado at Sat May 31 14:18:17 2025...
