Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Sat May 31 15:19:47 2025
| Host         : DESKTOP-5LO6MT6 running 64-bit major release  (build 9200)
| Command      : report_clock_utilization -file Top_clock_utilization_routed.rpt
| Design       : Top
| Device       : 7a100t-fgg484
| Speed File   : -1  PRODUCTION 1.23 2018-06-13
------------------------------------------------------------------------------------

Clock Utilization Report

Table of Contents
-----------------
1. Clock Primitive Utilization
2. Global Clock Resources
3. Global Clock Source Details
4. Clock Regions: Key Resource Utilization
5. Clock Regions : Global Clock Summary
6. Device Cell Placement Summary for Global Clock g0
7. Clock Region Cell Placement per Global Clock: Region X0Y1
8. Clock Region Cell Placement per Global Clock: Region X1Y1

1. Clock Primitive Utilization
------------------------------

+----------+------+-----------+-----+--------------+--------+
| Type     | Used | Available | LOC | Clock Region | Pblock |
+----------+------+-----------+-----+--------------+--------+
| BUFGCTRL |    1 |        32 |   0 |            0 |      0 |
| BUFH     |    0 |        96 |   0 |            0 |      0 |
| BUFIO    |    0 |        24 |   0 |            0 |      0 |
| BUFMR    |    0 |        12 |   0 |            0 |      0 |
| BUFR     |    0 |        24 |   0 |            0 |      0 |
| MMCM     |    0 |         6 |   0 |            0 |      0 |
| PLL      |    0 |         6 |   0 |            0 |      0 |
+----------+------+-----------+-----+--------------+--------+


2. Global Clock Resources
-------------------------

+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+-------------+--------------------------+-------------------+
| Global Id | Source Id | Driver Type/Pin | Constraint | Site          | Clock Region | Load Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock       | Driver Pin               | Net               |
+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+-------------+--------------------------+-------------------+
| g0        | src0      | BUFG/O          | None       | BUFGCTRL_X0Y0 | n/a          |                 2 |         380 |               0 |       10.000 | sys_clk_pin | sys_clk_IBUF_BUFG_inst/O | sys_clk_IBUF_BUFG |
+-----------+-----------+-----------------+------------+---------------+--------------+-------------------+-------------+-----------------+--------------+-------------+--------------------------+-------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


3. Global Clock Source Details
------------------------------

+-----------+-----------+-----------------+------------+-----------+--------------+-------------+-----------------+---------------------+--------------+---------------------+--------------+
| Source Id | Global Id | Driver Type/Pin | Constraint | Site      | Clock Region | Clock Loads | Non-Clock Loads | Source Clock Period | Source Clock | Driver Pin          | Net          |
+-----------+-----------+-----------------+------------+-----------+--------------+-------------+-----------------+---------------------+--------------+---------------------+--------------+
| src0      | g0        | IBUF/O          | IOB_X0Y74  | IOB_X0Y74 | X0Y1         |           1 |               0 |              10.000 | sys_clk_pin  | sys_clk_IBUF_inst/O | sys_clk_IBUF |
+-----------+-----------+-----------------+------------+-----------+--------------+-------------+-----------------+---------------------+--------------+---------------------+--------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


4. Clock Regions: Key Resource Utilization
------------------------------------------

+-------------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+
|                   | Global Clock |     BUFRs    |    BUFMRs    |    BUFIOs    |     MMCM     |      PLL     |      GT      |      PCI     |    ILOGIC    |    OLOGIC    |      FF      |     LUTM     |    RAMB18    |    RAMB36    |    DSP48E2   |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| Clock Region Name | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| X0Y0              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  2600 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y0              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |  1500 |    0 |   550 |    0 |    40 |    0 |    20 |    0 |    40 |
| X0Y1              |    1 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |  354 |  2000 |  117 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y1              |    1 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |   26 |  1900 |    0 |   650 |    0 |    60 |    0 |    30 |    0 |    40 |
| X0Y2              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  2000 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y2              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  1900 |    0 |   650 |    0 |    60 |    0 |    30 |    0 |    40 |
| X0Y3              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  2600 |    0 |   600 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y3              |    0 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     4 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |  1350 |    0 |   500 |    0 |    30 |    0 |    15 |    0 |    40 |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
* Global Clock column represents track count; while other columns represents cell counts


5. Clock Regions : Global Clock Summary
---------------------------------------

All Modules
+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y3 |  0 |  0 |
| Y2 |  0 |  0 |
| Y1 |  1 |  1 |
| Y0 |  0 |  0 |
+----+----+----+


6. Device Cell Placement Summary for Global Clock g0
----------------------------------------------------

+-----------+-----------------+-------------------+-------------+-------------+---------------+-------------+----------+----------------+----------+-------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock       | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net               |
+-----------+-----------------+-------------------+-------------+-------------+---------------+-------------+----------+----------------+----------+-------------------+
| g0        | BUFG/O          | n/a               | sys_clk_pin |      10.000 | {0.000 5.000} |         380 |        0 |              0 |        0 | sys_clk_IBUF_BUFG |
+-----------+-----------------+-------------------+-------------+-------------+---------------+-------------+----------+----------------+----------+-------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+-----+
|    | X0   | X1  |
+----+------+-----+
| Y3 |    0 |   0 |
| Y2 |    0 |   0 |
| Y1 |  354 |  26 |
| Y0 |    0 |   0 |
+----+------+-----+


7. Clock Region Cell Placement per Global Clock: Region X0Y1
------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF  | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net               |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-------------------+
| g0        | n/a   | BUFG/O          | None       |         354 |               0 | 354 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | sys_clk_IBUF_BUFG |
+-----------+-------+-----------------+------------+-------------+-----------------+-----+--------+------+-----+----+------+-----+---------+-------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


8. Clock Region Cell Placement per Global Clock: Region X1Y1
------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net               |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-------------------+
| g0        | n/a   | BUFG/O          | None       |          26 |               0 | 26 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | sys_clk_IBUF_BUFG |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+-------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts



# Location of BUFG Primitives 
set_property LOC BUFGCTRL_X0Y0 [get_cells sys_clk_IBUF_BUFG_inst]

# Location of IO Primitives which is load of clock spine

# Location of clock ports
set_property LOC IOB_X0Y74 [get_ports sys_clk]

# Clock net "sys_clk_IBUF_BUFG" driven by instance "sys_clk_IBUF_BUFG_inst" located at site "BUFGCTRL_X0Y0"
#startgroup
create_pblock {CLKAG_sys_clk_IBUF_BUFG}
add_cells_to_pblock [get_pblocks  {CLKAG_sys_clk_IBUF_BUFG}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="sys_clk_IBUF_BUFG"}]]]
resize_pblock [get_pblocks {CLKAG_sys_clk_IBUF_BUFG}] -add {CLOCKREGION_X0Y1:CLOCKREGION_X0Y1 CLOCKREGION_X1Y1:CLOCKREGION_X1Y1}
#endgroup
