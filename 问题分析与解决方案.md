# 药片装瓶系统问题分析与解决方案

## 问题现象
1. **上电后最左侧LED灯亮起**
2. **按下S6复位按钮后数码管和最右侧两个LED灯亮起**
3. **其他按钮均无反应**

## 问题分析

### 1. 最左侧LED灯亮起的原因
- **根本原因**：`light_control.v`模块中LED初始状态设置问题
- **具体位置**：绿色LED数组的初始化和控制逻辑
- **分析**：系统上电时，LED控制模块将绿色LED[0]（最左侧）设置为亮起状态

### 2. 复位后数码管和黄色LED亮起的原因
- **根本原因**：复位逻辑正常工作，系统进入设置状态
- **具体表现**：
  - 数码管开始显示（显示初始值0000）
  - 黄色LED亮起表示系统处于设置状态
  - 这是正常的系统行为

### 3. 按键无响应的根本原因

#### 3.1 Verilog代码问题
1. **Case语句不完整**
   - `keyboard.v`第77行：case语句缺少default分支
   - `main.v`第150行：case语句不完整
   - `light_control.v`第79行：case语句缺少default分支

2. **复位逻辑优先级问题**
   - `keyboard.v`第120行：寄存器同时有Set和Reset信号
   - `main.v`第106行：finish_bot寄存器复位逻辑问题

3. **状态机逻辑错误**
   - 键盘扫描状态机在某些条件下可能卡死
   - 消抖逻辑不够健壮

#### 3.2 约束文件问题
1. **缺少上拉电阻**
   - 按键输入信号没有配置上拉电阻
   - 键盘列信号需要上拉电阻保证高电平

2. **引脚驱动能力配置**
   - 键盘行输出信号需要适当的驱动能力配置

## 解决方案

### 1. 修正Verilog代码

#### 1.1 keyboard.v修正
```verilog
// 主要修正：
// 1. 添加完整的case语句default分支
// 2. 修正复位逻辑优先级
// 3. 优化消抖逻辑
// 4. 修正状态机逻辑
```

#### 1.2 main.v修正
```verilog
// 主要修正：
// 1. 修正case语句完整性
// 2. 修正阻塞/非阻塞赋值使用
// 3. 优化状态机逻辑
// 4. 修正复位逻辑
```

#### 1.3 light_control.v修正
```verilog
// 主要修正：
// 1. 添加case语句default分支
// 2. 优化LED控制逻辑
// 3. 修正计数器逻辑
```

### 2. 修正约束文件

#### 2.1 添加上拉电阻
```tcl
# 为所有输入信号添加上拉电阻
set_property PULLUP true [get_ports sys_rst_n]
set_property PULLUP true [get_ports start]
set_property PULLUP true [get_ports ack]
set_property PULLUP true [get_ports pil_mode]
set_property PULLUP true [get_ports display_mode]
set_property PULLUP true [get_ports {col[*]}]
```

#### 2.2 优化输出驱动配置
```tcl
# 为键盘行输出配置适当的驱动能力
set_property DRIVE 8 [get_ports {row[*]}]
set_property SLEW SLOW [get_ports {row[*]}]
```

### 3. 实施步骤

#### 步骤1：备份原始文件
- 备份所有原始Verilog文件
- 备份原始约束文件

#### 步骤2：应用修正
- 使用修正后的Verilog文件替换原始文件
- 更新约束文件

#### 步骤3：重新综合
- 在Vivado中重新运行综合
- 检查综合报告，确认警告消除

#### 步骤4：重新实现
- 运行实现流程
- 生成新的比特流文件

#### 步骤5：测试验证
- 下载到FPGA板
- 测试按键响应
- 验证LED和数码管显示

## 预期效果

### 修正后的系统行为
1. **上电后**：所有LED处于正确的初始状态
2. **复位后**：系统正常进入设置状态，黄色LED指示当前设置步骤
3. **按键响应**：矩阵键盘能够正常响应，数码管显示输入数值
4. **状态转换**：系统能够正常在设置和工作状态间切换

### 功能验证清单
- [ ] 矩阵键盘数字输入正常
- [ ] ACK确认按钮功能正常
- [ ] START开始按钮功能正常
- [ ] 数码管显示正确
- [ ] LED状态指示正确
- [ ] 系统状态机正常切换

## 技术要点

### 1. FPGA设计最佳实践
- 所有case语句必须包含default分支
- 避免同时使用阻塞和非阻塞赋值
- 复位信号优先级要明确
- 状态机设计要考虑所有可能状态

### 2. 约束文件配置
- 输入信号需要适当的上拉/下拉配置
- 输出信号需要合适的驱动能力
- 时钟约束要准确
- 引脚分配要与硬件匹配

### 3. 调试技巧
- 使用综合报告检查警告
- 使用仿真验证逻辑
- 分模块测试功能
- 逐步验证系统功能
