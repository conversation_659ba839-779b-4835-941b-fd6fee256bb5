# PowerShell脚本：修复药片装瓶系统项目
# 此脚本将修正后的Verilog文件复制到正确位置并更新项目

Write-Host "开始修复药片装瓶系统项目..." -ForegroundColor Green

# 定义路径
$sourceDir = "E:\vsPro\BUPT_Digital_Logic-main"
$currentDir = Get-Location
$fixedFiles = @(
    "keyboard_fixed.v",
    "main_fixed.v", 
    "light_control_fixed.v",
    "Top_fixed.v"
)

# 检查源目录是否存在
if (!(Test-Path $sourceDir)) {
    Write-Host "错误：源目录不存在: $sourceDir" -ForegroundColor Red
    exit 1
}

Write-Host "步骤1：备份原始文件..." -ForegroundColor Yellow

# 创建备份目录
$backupDir = Join-Path $sourceDir "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

# 备份原始文件
$originalFiles = @("keyboard.v", "main.v", "light_control.v", "Top.v")
foreach ($file in $originalFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $backupPath = Join-Path $backupDir $file
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $backupPath
        Write-Host "已备份: $file" -ForegroundColor Gray
    }
}

Write-Host "步骤2：复制修正后的文件..." -ForegroundColor Yellow

# 复制修正后的文件并重命名
$fileMapping = @{
    "keyboard_fixed.v" = "keyboard.v"
    "main_fixed.v" = "main.v"
    "light_control_fixed.v" = "light_control.v"
    "Top_fixed.v" = "Top.v"
}

foreach ($fixedFile in $fixedFiles) {
    $sourcePath = Join-Path $currentDir $fixedFile
    $targetFile = $fileMapping[$fixedFile]
    $targetPath = Join-Path $sourceDir $targetFile
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $targetPath -Force
        Write-Host "已复制: $fixedFile -> $targetFile" -ForegroundColor Green
    } else {
        Write-Host "警告：找不到修正文件: $fixedFile" -ForegroundColor Red
    }
}

Write-Host "步骤3：验证文件..." -ForegroundColor Yellow

# 验证文件是否存在
foreach ($file in $originalFiles) {
    $filePath = Join-Path $sourceDir $file
    if (Test-Path $filePath) {
        $fileSize = (Get-Item $filePath).Length
        Write-Host "✓ $file (大小: $fileSize 字节)" -ForegroundColor Green
    } else {
        Write-Host "✗ $file 缺失" -ForegroundColor Red
    }
}

Write-Host "`n修复完成！" -ForegroundColor Green
Write-Host "备份位置: $backupDir" -ForegroundColor Gray
Write-Host "`n接下来的步骤：" -ForegroundColor Cyan
Write-Host "1. 在Vivado中重新综合项目" -ForegroundColor White
Write-Host "2. 检查综合报告中的警告和错误" -ForegroundColor White
Write-Host "3. 重新实现和生成比特流" -ForegroundColor White
Write-Host "4. 下载到FPGA板进行测试" -ForegroundColor White

# 显示主要修正内容
Write-Host "`n主要修正内容：" -ForegroundColor Cyan
Write-Host "• 键盘模块：修正case语句完整性，优化消抖逻辑" -ForegroundColor White
Write-Host "• 主控模块：修正状态机逻辑，修正复位优先级" -ForegroundColor White  
Write-Host "• 灯控模块：修正case语句，优化LED控制逻辑" -ForegroundColor White
Write-Host "• 约束文件：添加上拉电阻，优化引脚配置" -ForegroundColor White

Write-Host "`n按任意键继续..." -ForegroundColor Gray
Read-Host
